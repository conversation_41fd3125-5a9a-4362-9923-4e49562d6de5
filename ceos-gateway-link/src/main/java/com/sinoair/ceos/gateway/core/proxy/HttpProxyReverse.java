package com.sinoair.ceos.gateway.core.proxy;

import com.alibaba.fastjson2.JSON;
import com.sinoair.ceos.gateway.common.dto.proxy.DoProxyResult;
import com.sinoair.ceos.gateway.common.dto.proxy.HttpProxyInfo;
import com.sinoair.ceos.gateway.common.dto.rocketmq.AppLogSaveParamDto;
import com.sinoair.ceos.gateway.common.util.HttpUtil;
import com.sinoair.ceos.gateway.common.util.StringUtil;
import org.apache.http.Header;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 *
 * 反向代理配置器
 * @author: 大雄
 */
public class HttpProxyReverse {

    private static Logger LOGGER = LoggerFactory.getLogger(HttpProxyReverse.class);
    // private static CloseableHttpClient httpClient;
    // private static HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
    private static RequestConfig DEFAULT_REQUEST_CONFIG = RequestConfig.custom()
            .setSocketTimeout(60 * 1000)
            .setConnectTimeout(60 * 1000).build();

//    static {
//        PoolingHttpClientConnectionManager connectionManager = null;
//        try {
//            //https config
//            TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;
//            SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
//                    .loadTrustMaterial(null, acceptingTrustStrategy)
//                    .build();
//
//            SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext
//                    ,null, null, NoopHostnameVerifier.INSTANCE);
//
//            Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
//                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
//                    .register("https", csf)
//                    .build();
//
//            connectionManager = new PoolingHttpClientConnectionManager(registry);
//            //最大连接数3000
//            connectionManager.setMaxTotal(3000);
//            //路由链接数400
//            connectionManager.setDefaultMaxPerRoute(400);
//
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//
//        RequestConfig requestConfig = RequestConfig.custom()
//                .setSocketTimeout(60000)
//                .setConnectTimeout(60000)
//                .setConnectionRequestTimeout(10000)
//                //禁止重定向
//                .setRedirectsEnabled(false)
//                .build();
//        httpClient = HttpClients.custom().setDefaultRequestConfig(requestConfig)
//                .setConnectionManager(connectionManager)
//                .evictExpiredConnections()
//                .evictIdleConnections(30, TimeUnit.SECONDS)
//                .build();
//        requestFactory.setHttpClient(httpClient);
//    }

    private static String createTargetUri(String url, HttpServletRequest request) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder(url);
        uriBuilder.setCharset(Charset.forName("UTF-8"));
        Enumeration<String> parameterNames = request.getParameterNames();
        List<NameValuePair> nvps = new ArrayList<>();
        while (parameterNames.hasMoreElements()){
            String name = parameterNames.nextElement();
            nvps.add(new BasicNameValuePair(name,request.getParameter(name)));
        }
        uriBuilder.setParameters(nvps);
        return uriBuilder.toString();
    }

    /**
     * 创建get请求
     * @param proxyInfo
     * @param request
     * @return
     * @throws URISyntaxException
     */
    private static HttpRequestBase createGet(HttpProxyInfo proxyInfo, HttpServletRequest request,AppLogSaveParamDto logInfo) throws URISyntaxException {
        return new HttpGet(createTargetUri(proxyInfo.getTargetUrl(),request));
    }

    /**
     * 创建post 请求
     * @param proxyInfo
     * @param request
     * @return
     * @throws URISyntaxException
     */
    private static HttpRequestBase createPost(HttpProxyInfo proxyInfo, HttpServletRequest request,AppLogSaveParamDto logInfo) throws URISyntaxException {
        // HttpPost post = new HttpPost(createTargetUri(proxyInfo.getTargetUrl(),request));
        // 日志记录
        String readRequest = HttpUtil.readRequest(request);
        Enumeration<String> parameterNames = request.getParameterNames();
        Map<String,String> parameterMap = new HashMap<>();
        while (parameterNames.hasMoreElements()){
            String key = parameterNames.nextElement();
            String value = request.getParameter(key);
            parameterMap.put(key,value);
        }
        HttpPost post = null;
        // post设置
        if (StringUtil.isNotEmpty(readRequest)){
            // json请求设置
            post = new HttpPost(createTargetUri(proxyInfo.getTargetUrl(),request));
            StringEntity entity = new StringEntity(readRequest, "UTF-8");
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            post.setEntity(entity);
            // 日志记录
            logInfo.setRequest(readRequest);
        }else{
            post = new HttpPost(proxyInfo.getTargetUrl());
            // form设置
            if (!parameterMap.keySet().isEmpty()){
                List<NameValuePair> nvps = new ArrayList<>();
                for (String key : parameterMap.keySet()){
                    nvps.add(new BasicNameValuePair(key,parameterMap.get(key)));
                }
                post.setEntity(new UrlEncodedFormEntity(nvps, StandardCharsets.UTF_8));
                logInfo.setRequest(JSON.toJSONString(parameterMap));
            }
        }

        return post;
    }


    /**
     * 处理代理请求
     * @param proxyInfo
     * @param request
     * @param response
     */
    public static DoProxyResult doProxy(HttpProxyInfo proxyInfo, HttpServletRequest request, HttpServletResponse response){
        if (proxyInfo != null){
            AppLogSaveParamDto logDto = new AppLogSaveParamDto();
            Map<String,String> logResponseHeader = new HashMap<>();
            String responseContent = null;
            int responseStatus = 0;
            String responseContentType = "application/json;charset=UTF-8";
            Map<String,String> headers = null;
            // 方法匹配异常
            if (!request.getMethod().toUpperCase().equals(proxyInfo.getTargetMethod())){
                responseContent = "{\"code\":\"405\",\"message\":\"METHOD_NOT_ALLOWED\"}";
                responseStatus = 405;
            }else{
                CloseableHttpClient client = null;
                CloseableHttpResponse execute = null;
                try {
                    HttpRequestBase targetRequest = null;
                    switch (request.getMethod().toUpperCase()){
                        case "GET":
                            targetRequest = createGet(proxyInfo, request,logDto);
                            break;
                        case "POST":
                            targetRequest = createPost(proxyInfo, request,logDto);
                            break;
                        default:
                            throw new IllegalStateException("Unexpected value: " + request.getMethod());
                    }

                    // 请求头配置
                    Enumeration<String> headerNames = request.getHeaderNames();
                    Map<String,String> headerMap = new HashMap<>();
                    while (headerNames.hasMoreElements()){
                        String headerName = headerNames.nextElement();
                        targetRequest.addHeader(headerName,request.getHeader(headerName));
                        headerMap.put(headerName,request.getHeader(headerName));
                    }

                    logDto.setRequestHeader(JSON.toJSONString(headerMap));

                    // 请求超时配置
                    if (proxyInfo.getTargetTimeout() != null && proxyInfo.getTargetTimeout() > 0){
                        RequestConfig requestConfig = RequestConfig.custom()
                                .setSocketTimeout(proxyInfo.getTargetTimeout())
                                .setConnectTimeout(proxyInfo.getTargetTimeout())
                                .setRedirectsEnabled(false)
                                .build();
                        targetRequest.setConfig(requestConfig);
                        client = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();
                    }else{
                        // 默认30秒
                        targetRequest.setConfig(DEFAULT_REQUEST_CONFIG);
                        client = HttpClients.custom().setDefaultRequestConfig(DEFAULT_REQUEST_CONFIG).build();

                    }
                    // 清除Content-Length属性
                    targetRequest.removeHeaders("Content-Length");
                    execute = client.execute(targetRequest);
                    // 响应状态码
                    // response.setStatus(execute.getStatusLine().getStatusCode());
                    responseStatus = execute.getStatusLine().getStatusCode();
                    // 响应头
                    Header[] allHeaders = execute.getAllHeaders();
                    headers = new HashMap<>();
                    for (int i = 0; i < allHeaders.length; i++) {
                        Header h = allHeaders[i];
                        //response.addHeader(h.getName(),h.getValue());
                        if (h.getName().startsWith("-GATEWAY-")){
                            logResponseHeader.put(h.getName(),h.getValue());
                        }else{
                            headers.put(h.getName(),h.getValue());
                        }
                    }
                    headers.remove("Content-Length");
                    headers.remove("Transfer-Encoding");
                    headers.remove("Vary");
                    headers.remove("transfer-encoding");
                    // 响应体
                    //PrintWriter writer = response.getWriter();
                    if (execute.getEntity() != null){
                        // response.setContentType(execute.getEntity().getContentType().getValue());
                        // writer.write(EntityUtils.toString(execute.getEntity(),"UTF-8"));
                        if (execute.getEntity().getContentType() != null){
                            responseContentType = execute.getEntity().getContentType().getValue();
                        }
                        responseContent = EntityUtils.toString(execute.getEntity(),"UTF-8");
                        EntityUtils.consume(execute.getEntity());
                    }
                }catch (Exception e) {
                    LOGGER.error(e.getMessage(),e);
                    if (e.getMessage().contains("Read timed out")){
                        String uuid = UUID.randomUUID().toString();
                        StringBuilder error = new StringBuilder();
                        error.append("【").append(uuid).append("】请求超时：").append(proxyInfo.getTargetUrl()).append("\r\n");

                        if (StringUtil.isNotEmpty(logDto.getRequest())){
                            error.append("【").append(uuid).append("】请求参数：").append(logDto.getRequest()).append("\r\n");
                        }
                        LOGGER.error(error.toString());
                    }
                    responseContent = "{\"code\":\"500\",\"message\":\"INTERNAL_SERVER_ERROR\"}";
                    responseStatus = 500;
                } finally {
                    if (execute != null){
                        try {
                            execute.close();
                        } catch (IOException e) {
                            LOGGER.error(e.getMessage(),e);
                        }
                    }
                    if (client != null){
                        try {
                            client.close();
                        } catch (IOException e) {
                            LOGGER.error(e.getMessage(),e);
                        }
                    }
                }
            }
            // 写入response
            // HttpUtil.writeResponse(response,responseContentType,responseStatus,headers,responseContent);
            // 存储日志
            if (proxyInfo.getSwitchLog() == 1){
                logDto.setResponseHeader(JSON.toJSONString(headers));
                logDto.setResponse(responseContent);
                logDto.setAppCode("gateway");
                logDto.setAppName("API网关");
                logDto.setAppDeployInfo(proxyInfo.getTargetUrl());
                if (logResponseHeader.get("-GATEWAY-BIZ-CODE") != null){
                    logDto.setBizCode(HttpUtil.urlEncode(logResponseHeader.get("-GATEWAY-BIZ-CODE")));
                }
                if (logResponseHeader.get("-GATEWAY-BIZ-TYPE") != null){
                    logDto.setBizType(HttpUtil.urlEncode(logResponseHeader.get("-GATEWAY-BIZ-TYPE")));
                }
                if (logResponseHeader.get("-GATEWAY-STATUS") != null){
                    logDto.setLogStatus(HttpUtil.urlEncode(logResponseHeader.get("-GATEWAY-STATUS")));
                }
                logDto.setFunction(proxyInfo.getProxyName());
                String url = request.getRequestURI();
                if (StringUtil.isNotEmpty(request.getQueryString())){
                    url = url + "?" + request.getQueryString();
                }
                logDto.setUrl(url);
                logDto.setLogLevel("INFO");
                logDto.setOperationTime(System.currentTimeMillis());
                return new DoProxyResult(logDto,response,responseContentType,responseStatus,headers,responseContent);
            }else{
                return new DoProxyResult(null,response,responseContentType,responseStatus,headers,responseContent);
            }
        }else{
            return null;
        }
    }
}
