package com.sinoair.ceos.gateway.cache;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import com.sinoair.ceos.gateway.common.dto.api.HttpResultDto;
import com.sinoair.ceos.gateway.common.dto.app.AppInfo;
import com.sinoair.ceos.gateway.common.dto.app.AppResource;
import com.sinoair.ceos.gateway.common.dto.app.GatewaySyncInfoDto;
import com.sinoair.ceos.gateway.common.exception.CeosGatewayCallException;
import com.sinoair.ceos.gateway.common.util.HttpUtil;
import com.sinoair.ceos.gateway.common.util.SignatureUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: daxiong
 */
@Component
public class GatewayCache {

    private static Logger LOGGER = LoggerFactory.getLogger(GatewayCache.class);

    @Value("${ceos-gateway.console.app-key}")
    private String appKey;

    @Value("${ceos-gateway.console.app-secret}")
    private String appSecret;

    @Value("${ceos-gateway.console.api.app-info}")
    private String apiUrl;

    @Value(("${spring.application.name}"))
    private String applicationName;

    private String gatewayVersion;

    private static final String RESOURCES_CODE_GATEWAY_INFO = "SYS-CEOS-GATEWAY-INFO";

    /**
     * 缓存信息
     */
    private Map<String, AppInfo> appInfoCache;

    @Autowired
    private GatewayProxyCache gatewayProxyCache;


    private Map<String, RateLimiter> limiterMap = new ConcurrentHashMap<>();

    /**
     * 查询是否缓存
     * @return
     */
    public boolean checkAppInfoCache(){
        return  appInfoCache != null;
    }

    /**
     * 根据appKey 查询订阅信息
     * @param appKey
     * @return
     */
    public AppInfo getAppInfo(String appKey){
        return appInfoCache.get(appKey);
    }

    /**
     * 获取限流器
     * @param appKey
     * @param resCode
     * @return
     */
    public RateLimiter getLimiter(String appKey,String resCode){
        return this.limiterMap.get(appKey+"_"+resCode);
    }

    /**
     * 获取应用授权信息
     */
    public void refreshCache(){
        synchronized (this){
            Long timestamp = System.currentTimeMillis();
            Map<String,String> header = new HashMap<>();
            header.put("Timestamp",timestamp+"");
            header.put("Resource-Code",RESOURCES_CODE_GATEWAY_INFO);
            header.put("App-Key",appKey);
            header.put("Signature", SignatureUtil.createSign(appKey,appSecret,RESOURCES_CODE_GATEWAY_INFO,this.applicationName,timestamp));
            HttpResultDto httpResultDto = HttpUtil.post(this.apiUrl, header, null, null, this.applicationName, 10_000);
            JSONObject apiResult = JSONObject.parseObject(httpResultDto.getResponseData());
            if ("200".equals(apiResult.getString("code"))){
                GatewaySyncInfoDto gatewaySyncInfoDto = apiResult.getObject("result",GatewaySyncInfoDto.class);
                if (this.getGatewayVersion() == null || !this.getGatewayVersion().equals(gatewaySyncInfoDto.getGatewayVersion())){
                    Map<String,AppInfo> map = new HashMap<>();
                    for(AppInfo appInfo : gatewaySyncInfoDto.getAppInfos()){
                        map.put(appInfo.getAppKey(),appInfo);
                        for (AppResource appResource : appInfo.getAppResources()){
                            String key = appKey+"_"+appResource.getResCode();
                            RateLimiter rateLimiter = limiterMap.get(key);
                            if (rateLimiter == null){
                                rateLimiter = RateLimiter.create(appResource.getRateLimit());
                                this.limiterMap.put(key,rateLimiter);
                            }else{
                                double rate = rateLimiter.getRate();
                                if (rate != Double.valueOf(appResource.getRateLimit()).doubleValue()){
                                    rateLimiter.setRate(appResource.getRateLimit());
                                }
                            }
                        }
                    }
                    this.gatewayVersion = gatewaySyncInfoDto.getGatewayVersion();
                    appInfoCache = map;
                }
                //代理信息配置同步
                this.gatewayProxyCache.refreshCache(gatewaySyncInfoDto.getGatewayProxySyncInfo());
            }else{
                throw new CeosGatewayCallException("获取Gateway信息解析异常："+httpResultDto.getResponseData());
            }
        }
    }

    @PostConstruct
    public void init() throws Exception {
        boolean initFlag = false;
        // 初始化 重试5次，失败后退出应用
        for (int i = 0; i < 30; i++) {
            try {
                this.refreshCache();
                initFlag = true;
                break;
            }catch (Exception e){
                e.printStackTrace();
                LOGGER.error(e.getMessage(),e);
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ex) {
                    ex.printStackTrace();
                }
            }
        }
        if (!initFlag){
            throw new Exception("获取远程服务异常，服务启动失败:"+apiUrl);
        }
    }

    public String getGatewayVersion() {
        return gatewayVersion;
    }
}
