package com.sinoair.ceos.gateway.core.rocketmq;

import com.alibaba.fastjson2.JSON;
import com.sinoair.ceos.gateway.common.dto.rocketmq.AppLogSaveParamDto;
import com.sinoair.ceos.gateway.common.mongo.GatewayApiLogEntity;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: 大雄
 * @Date: 2023/9/18 14:43
 * @Description:
 */
@Component
public class CeosLogProducer {

    private static Logger LOGGER = LoggerFactory.getLogger(CeosLogProducer.class);

    private static final String TOPIC_CEOS_LOG_STORE = "biz-log-store-app-log-save";


    /**
     * 存储存贮log标志
     */
    private static final String TAG_APP_LOG_SAVE = "ceos-app-log-save";

    /**
     * ceos_app_log
     */
    private String destinationLog = TOPIC_CEOS_LOG_STORE+":"+TAG_APP_LOG_SAVE;


    /**
     * 消费主题
     */
    private static final String TOPIC_CEOS_GATEWAY_LOG_STORE = "biz-log-store-gateway-log-save";


    /**
     * 存储存贮log标志
     */
    private static final String TAG_GATEWAY_LOG_SAVE = "gateway-app-log-save";

    /**
     * ceos_gateway_api_log
     */
    private String destinationGatewayLog = TOPIC_CEOS_GATEWAY_LOG_STORE+":"+TAG_GATEWAY_LOG_SAVE;

    @Resource
    private MongoTemplate mongoTemplate;


    @Resource
    @Qualifier("rocketMQTemplate")
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 异步存储日志
     * @param appLogSaveParamDto
     */
    public void saveLogAsync(AppLogSaveParamDto appLogSaveParamDto){
        try {
            String key = null;
            if (appLogSaveParamDto.getSysId() == null){
                key = appLogSaveParamDto.getSysId();
            }else{
                key = appLogSaveParamDto.getBizCode();
            }
            Message<String> mqMessage = MessageBuilder.withPayload(JSON.toJSONString(appLogSaveParamDto))
                    .setHeader("KEYS", key)
                    .build();
            rocketMQTemplate.asyncSend(destinationLog, mqMessage, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    LOGGER.debug("日志存储成功{}",sendResult.getMsgId());
                }

                @Override
                public void onException(Throwable throwable) {
                    LOGGER.error("日志存储失败："+throwable.getMessage(),throwable);
                }
            });
        }catch (Exception e){
            LOGGER.error(e.getMessage(),e);
        }
    }

    /**
     * 异步存储网关日志
     * @param entity
     */
    public void saveGatewayLogSync(GatewayApiLogEntity entity){
        try {
            String key = null;
            if (entity.getMessageId() == null){
                key = entity.getMessageId();
            }else{
                key = entity.getSearchCode();
            }
            Message<String> mqMessage = MessageBuilder.withPayload(JSON.toJSONString(entity))
                    .setHeader("KEYS", key)
                    .build();
            rocketMQTemplate.asyncSend(destinationGatewayLog, mqMessage, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    LOGGER.debug("日志存储成功{}",sendResult.getMsgId());
                }

                @Override
                public void onException(Throwable throwable) {
                    LOGGER.error("日志存储失败："+throwable.getMessage(),throwable);
                    try {
                        entity.setSource("CEOS-GATEWAY-LINK");
                        mongoTemplate.insert(entity);
                    }catch (Exception e){
                        LOGGER.error(e.getMessage(),e);
                    }

                }
            });
        }catch (Exception e){
            LOGGER.error(e.getMessage(),e);
        }
    }
}
