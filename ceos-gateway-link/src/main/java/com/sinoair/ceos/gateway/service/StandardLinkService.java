package com.sinoair.ceos.gateway.service;


import com.sinoair.ceos.gateway.common.dto.api.RpcResult;
import com.sinoair.ceos.gateway.common.dto.app.AppResource;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: daxiong
 */
public interface StandardLinkService {

    /**
     * 处理并转发
     * @param request
     * @param messageId
     * @param requestBody
     * @return
     */
    RpcResult rpcLink(HttpServletRequest request, String messageId,String requestBody);

    /**
     * 释放幂等锁
     * @param requestBody
     * @param appResource
     * @param appKey
     */
    void unlockIdempotent(String requestBody, AppResource appResource, String appKey);


    /**
     * 获取资源信息
     * @param request
     * @return
     */
    AppResource getAppResource(HttpServletRequest request);
}
