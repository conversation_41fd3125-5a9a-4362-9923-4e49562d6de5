package com.sinoair.ceos.gateway.core.config;

import org.springframework.boot.web.server.ErrorPage;
import org.springframework.boot.web.server.ErrorPageRegistrar;
import org.springframework.boot.web.server.ErrorPageRegistry;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;

/**
 * @Author: daxiong
 */
@Configuration
public class AppErrorPageRegistrar implements ErrorPageRegistrar {

    @Override
    public void registerErrorPages(ErrorPageRegistry registry) {
        registry.addErrorPages(new ErrorPage(HttpStatus.NOT_FOUND, "/error/404"));
        registry.addErrorPages(new ErrorPage(HttpStatus.INTERNAL_SERVER_ERROR, "/error/500"));
        registry.addErrorPages(new ErrorPage(HttpStatus.BAD_REQUEST, "/error/400"));
        registry.addErrorPages(new ErrorPage(HttpStatus.UNAUTHORIZED, "/error/401"));
        registry.addErrorPages(new ErrorPage(HttpStatus.FORBIDDEN, "/error/403"));
        registry.addErrorPages(new ErrorPage(HttpStatus.METHOD_NOT_ALLOWED, "/error/405"));
    }
}
