package com.sinoair.ceos.gateway.timer;

import com.sinoair.ceos.gateway.cache.GatewayCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * @Author: daxiong
 */
@Configuration
public class CeosGatewayInfoTimer {

    @Autowired
    private GatewayCache gatewayCache;

    @Scheduled(fixedDelay = 30_000L)
    public void refreshGatewayAppInfo(){
        gatewayCache.refreshCache();
    }

}
