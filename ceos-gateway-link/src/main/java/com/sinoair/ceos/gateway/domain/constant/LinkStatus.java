package com.sinoair.ceos.gateway.domain.constant;

/**
 * 系统状态码
 * @Author: daxiong
 */
public enum LinkStatus {
    OK("200","Success"),
    ERROR_TIMESTAMP("E01","时间戳过期或无效"),
    ERROR_APP_KEY("E02","无效的appKey"),
    ERROR_RESOURCES_CODE("E03","资源CODE无效或无权限"),
    ERROR_SIGNATURE("E04","数据签名无效"),
    ERROR_PARAM("E05","参数无效"),
    ERROR_JSON("E06","非法JSON参数"),
    ERROR_SERVER("E07","业务系统异常，请稍候再试"),
    ERROR_UPGRADE("E08","业务系统正在升级，请稍候再试"),
    ERROR_MAX_VISIT("E09","您的请求太过频繁，请稍候再试"),
    ERROR_IDEMPOTENT("E10","您的请求正在处理，请勿重复提交");

    private String code,message;

    LinkStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
