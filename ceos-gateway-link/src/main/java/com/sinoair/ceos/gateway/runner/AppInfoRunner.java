package com.sinoair.ceos.gateway.runner;

import com.sinoair.ceos.gateway.cache.GatewayCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * @Author: daxiong
 */
@Component
public class AppInfoRunner implements CommandLineRunner {

    @Autowired
    private GatewayCache gatewayCache;

    @Override
    public void run(String... args) throws Exception {
        this.gatewayCache.refreshCache();
    }
}
