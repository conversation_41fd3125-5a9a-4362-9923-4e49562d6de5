package com.sinoair.ceos.gateway.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.sinoair.ceos.gateway.cache.GatewayCache;
import com.sinoair.ceos.gateway.common.dto.api.ApiResult;
import com.sinoair.ceos.gateway.common.dto.api.RpcResult;
import com.sinoair.ceos.gateway.common.dto.app.AppInfo;
import com.sinoair.ceos.gateway.common.dto.app.AppResource;
import com.sinoair.ceos.gateway.common.dto.rocketmq.AppLogSaveParamDto;
import com.sinoair.ceos.gateway.common.mongo.GatewayApiLogEntity;
import com.sinoair.ceos.gateway.common.util.DateUtil;
import com.sinoair.ceos.gateway.core.rocketmq.CeosLogProducer;
import com.sinoair.ceos.gateway.service.LogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;

/**
 * @Author: daxiong
 */
@Service
public class LogServiceImpl implements LogService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private GatewayCache gatewayCache;

    @Autowired
    private CeosLogProducer ceosLogProducer;

    @Value("${ceos-gateway.link.log-save-type}")
    private String gatewayLogSaveType;

    @Override
    public void saveLog(AppResource appResource,
                        String messageId,
                        Date start,
                        HttpServletRequest request,
                        String requestBody,
                        ApiResult apiResult,
                        Map<String,String> responseHeader,
                        RpcResult rpcResult){
        if ( appResource != null && appResource.getSwitchLog() == 1){
            // 日志存储
            Date end = new Date();
            // messageId
            GatewayApiLogEntity gatewayApiLogEntity = new GatewayApiLogEntity();
            gatewayApiLogEntity.setMessageId(messageId);
            // type
            gatewayApiLogEntity.setMessageType("GATEWAY-API");
            // createTime
            Date now = new Date();
            gatewayApiLogEntity.setCreateTime(now);
            gatewayApiLogEntity.setYearMonth(DateUtil.parseDateStr(DateUtil.DATE_yyyy_MM,now));
            gatewayApiLogEntity.setYear(DateUtil.getYear(now));
            gatewayApiLogEntity.setMonth(DateUtil.getMonth(now));
            gatewayApiLogEntity.setDay(DateUtil.parseDateStr(DateUtil.DATE_dd,now));
            // start
            gatewayApiLogEntity.setStartTime(start);
            // end
            gatewayApiLogEntity.setEndTime(end);
            // Response Time
            gatewayApiLogEntity.setResponseTime(end.getTime() - start.getTime());
            // appKey
            gatewayApiLogEntity.setAppKey(request.getHeader("App-Key"));
            // resCode
            gatewayApiLogEntity.setResCode(request.getHeader("Resource-Code"));

            // requestBody
            gatewayApiLogEntity.setRequestBody(requestBody);
            // response
            gatewayApiLogEntity.setResponseBody(JSON.toJSONString(apiResult));
            if (appResource != null){
                // resourceInfo
                gatewayApiLogEntity.setResourceInfo(JSONObject.toJSONString(appResource));
                gatewayApiLogEntity.setResName(appResource.getResName());
            }
            JSONObject requestHeader = new JSONObject();
            requestHeader.put("Timestamp", request.getHeader("Timestamp"));
            requestHeader.put("Resource-Code", request.getHeader("Resource-Code"));
            requestHeader.put("App-Key", request.getHeader("App-Key"));
            requestHeader.put("Signature", request.getHeader("Signature"));
            gatewayApiLogEntity.setRequestHeader(requestHeader.toJSONString());
            if (responseHeader != null){
                gatewayApiLogEntity.setSearchCode(responseHeader.get("Search-Code"));
                gatewayApiLogEntity.setSearchType(responseHeader.get("Search-Type"));
            }
           /* if (rpcResult != null && rpcResult.getAppInfo() != null){
                gatewayApiLogEntity.setAppName(rpcResult.getAppInfo().getAppName());
            }*/
            AppInfo appInfo = gatewayCache.getAppInfo(gatewayApiLogEntity.getAppKey());
            if (appInfo != null){
                gatewayApiLogEntity.setAppName(appInfo.getAppName());
            }
            String str = request.getRequestURI();
            String uri = str.split("\\?")[0];
            gatewayApiLogEntity.setApiUrl(uri);
            if ("async".equalsIgnoreCase(gatewayLogSaveType)){
                this.saveGatewayLogAsync(gatewayApiLogEntity);
            }else{
                gatewayApiLogEntity.setSource("CEOS-GATEWAY-LINK");
                this.mongoTemplate.insert(gatewayApiLogEntity);
            }
        }
    }

    /**
     * 异步存储日志
     *
     * @param appLogSaveParamDto
     */
    @Override
    public void saveCeosLogAsync(AppLogSaveParamDto appLogSaveParamDto) {
        ceosLogProducer.saveLogAsync(appLogSaveParamDto);
    }

    /**
     * 异步存储gatewayLog
     *
     * @param gatewayApiLogEntity
     */
    @Override
    public void saveGatewayLogAsync(GatewayApiLogEntity gatewayApiLogEntity) {
        gatewayApiLogEntity.setSource("MQ");
        this.ceosLogProducer.saveGatewayLogSync(gatewayApiLogEntity);
    }

    /**
     * 同步存储gatewayLog
     *
     * @param gatewayApiLogEntity
     */
    @Override
    public void saveGatewayLog(GatewayApiLogEntity gatewayApiLogEntity) {
        gatewayApiLogEntity.setSource("CEOS-GATEWAY-LINK");
        this.mongoTemplate.insert(gatewayApiLogEntity);
    }


}
