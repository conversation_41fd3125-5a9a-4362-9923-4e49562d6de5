package com.sinoair.ceos.gateway.controller.v1;

import com.sinoair.ceos.gateway.common.dto.api.ApiResult;
import com.sinoair.ceos.gateway.common.dto.api.RpcResult;
import com.sinoair.ceos.gateway.common.dto.app.AppResource;
import com.sinoair.ceos.gateway.common.exception.CeosGatewayCallException;
import com.sinoair.ceos.gateway.common.exception.CeosGatewayException;
import com.sinoair.ceos.gateway.common.util.ApiResultBuilder;
import com.sinoair.ceos.gateway.common.util.HttpUtil;
import com.sinoair.ceos.gateway.domain.constant.LinkStatus;
import com.sinoair.ceos.gateway.service.LogService;
import com.sinoair.ceos.gateway.service.StandardLinkService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * 对接入口
 * @Author: daxiong
 */
@RestController
public class StandardLinkController {

    private static Logger LOGGER = LoggerFactory.getLogger(StandardLinkController.class);

    @Autowired
    private StandardLinkService standardLinkService;

    @Autowired
    private LogService logService;

    @PostMapping("/standard/v1/link")
    public ResponseEntity linkV1(HttpServletRequest request){
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        Date start = new Date();
        RpcResult rpcResult = null;
        AppResource appResource = this.standardLinkService.getAppResource(request);
        Map<String,String> responseHeader = null;
        // 获取请求体信息
        String requestBody = HttpUtil.readRequest(request);
        try {
            rpcResult = standardLinkService.rpcLink(request,messageId,requestBody);
            apiResult = rpcResult.getApiResult();
            apiResult.setMessageId(messageId);
            appResource = rpcResult.getAppResource();
            responseHeader = rpcResult.getResponseHeader();
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else if (e instanceof CeosGatewayCallException){
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code(LinkStatus.ERROR_SERVER.getCode())
                        .message(LinkStatus.ERROR_SERVER.getMessage())
                        .newApiResult();
                appResource = ((CeosGatewayCallException) e).getAppResource();

            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code(LinkStatus.ERROR_SERVER.getCode())
                        .message(LinkStatus.ERROR_SERVER.getMessage())
                        .newApiResult();
            }
        }finally {
            // 幂等解锁
            try {
                this.standardLinkService.unlockIdempotent(requestBody,appResource,request.getHeader("App-Key"));
            }catch (Exception e){
                LOGGER.error(e.getMessage(),e);
            }
            // 日志存储
            try {
                logService.saveLog(appResource,messageId,start,request,requestBody,apiResult,responseHeader,rpcResult);
            }catch (Exception e){
                LOGGER.error(e.getMessage(),e);
                LOGGER.error("存储日志异常");
            }
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }
}
