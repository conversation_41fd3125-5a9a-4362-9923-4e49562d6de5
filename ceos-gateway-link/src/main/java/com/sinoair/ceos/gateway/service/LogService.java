package com.sinoair.ceos.gateway.service;

import com.sinoair.ceos.gateway.common.dto.api.ApiResult;
import com.sinoair.ceos.gateway.common.dto.api.RpcResult;
import com.sinoair.ceos.gateway.common.dto.app.AppResource;
import com.sinoair.ceos.gateway.common.dto.rocketmq.AppLogSaveParamDto;
import com.sinoair.ceos.gateway.common.mongo.GatewayApiLogEntity;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;

/**
 * @Author: daxiong
 */
public interface LogService {

    /**
     *
     * @param appResource
     * @param messageId
     * @param start
     * @param request
     * @param requestBody
     * @param apiResult
     */
    void saveLog(AppResource appResource,
                 String messageId,
                 Date start,
                 HttpServletRequest request,
                 String requestBody,
                 ApiResult apiResult,
                 Map<String,String> responseHeader,
                 RpcResult rpcResult);

    /**
     * 异步存储日志
     * @param appLogSaveParamDto
     */
    void saveCeosLogAsync(AppLogSaveParamDto appLogSaveParamDto);

    /**
     * 异步存储gatewayLog
     * @param gatewayApiLogEntity
     */
    void saveGatewayLogAsync(GatewayApiLogEntity gatewayApiLogEntity);

    /**
     * 同步存储gatewayLog
     * @param gatewayApiLogEntity
     */
    void saveGatewayLog(GatewayApiLogEntity gatewayApiLogEntity);

}
