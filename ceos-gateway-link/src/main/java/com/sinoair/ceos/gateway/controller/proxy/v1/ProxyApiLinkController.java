package com.sinoair.ceos.gateway.controller.proxy.v1;

import com.sinoair.ceos.gateway.cache.GatewayProxyCache;
import com.sinoair.ceos.gateway.common.dto.proxy.DoProxyResult;
import com.sinoair.ceos.gateway.common.dto.proxy.HttpProxyInfo;
import com.sinoair.ceos.gateway.common.util.HttpUtil;
import com.sinoair.ceos.gateway.core.proxy.HttpProxyReverse;
import com.sinoair.ceos.gateway.service.LogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * @author: 大雄
 */
@Controller
@RequestMapping("proxy/api/v1")
public class ProxyApiLinkController {

    private static Logger LOGGER = LoggerFactory.getLogger(ProxyApiLinkController.class);

    @Autowired
    private LogService logService;

    @Resource
    private GatewayProxyCache gatewayProxyCache;

    /**
     * /gateway/proxy/api/v1/link/xxxx
     * 代理入口
     * @param state
     * @param request
     * @param response
     */
    @RequestMapping(value = "link/{state}")
    public void link(@PathVariable("state")String state, HttpServletRequest request, HttpServletResponse response){
        String uuid = UUID.randomUUID().toString();
        LOGGER.debug("["+uuid+"]开始执行");
        Long startTime = System.currentTimeMillis();
        HttpProxyInfo httpProxy = this.gatewayProxyCache.getHttpProxy(state);
        if (httpProxy == null){
            //资源无效
            HttpUtil.writeResponse(response,
                    "application/json;charset=GBK",
                    HttpStatus.SERVICE_UNAVAILABLE.value(),
                    null,
                    "{\"code\":\"503\",\"message\":\"SERVICE_UNAVAILABLE\"}"
                    );
        }else{
            try {
                LOGGER.debug("["+uuid+"]开始远程调用");
                DoProxyResult proxyResult = HttpProxyReverse.doProxy(httpProxy, request, response);
                LOGGER.debug("["+uuid+"]开始远程调用结束，开始存储日志");
                if (proxyResult != null && proxyResult.getLogDto() != null && httpProxy.getSwitchLog() == 1){
                    Long endTime = System.currentTimeMillis();
                    proxyResult.getLogDto().setRequestTime(startTime);
                    proxyResult.getLogDto().setResponseTime(endTime);
                    proxyResult.getLogDto().setProcessingTime(endTime - startTime);
                    logService.saveCeosLogAsync(proxyResult.getLogDto());
                }
                LOGGER.debug("["+uuid+"]日志存储结束，开始返回数据");
                HttpUtil.writeResponse(proxyResult.getResponse(),
                        proxyResult.getContentType(),
                        proxyResult.getHttpStatus(),
                        proxyResult.getHeaders(),
                        proxyResult.getContent());
                LOGGER.debug("["+uuid+"]数据返回完成");
            }catch (Exception e){
                LOGGER.error(e.getMessage(),e);
                HttpUtil.writeResponse(response,
                        "application/json;charset=UTF-8",
                        HttpStatus.INTERNAL_SERVER_ERROR.value(),
                        null,
                        "{\"code\":\"500\",\"message\":\"Internal Server Error\"}"
                );
            }
            LOGGER.debug("["+uuid+"]结束");
        }
    }

}
