package com.sinoair.ceos.gateway.cache;

import com.sinoair.ceos.gateway.common.dto.proxy.GatewayProxySyncInfo;
import com.sinoair.ceos.gateway.common.dto.proxy.HttpProxyInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: 大雄
 */
@Configuration
public class GatewayProxyCache {

    private static Logger LOGGER = LoggerFactory.getLogger(GatewayProxyCache.class);



    /**
     * 缓存信息
     */
    private ConcurrentHashMap<String, HttpProxyInfo> httpProxyCache = new ConcurrentHashMap<>();

    /**
     * 获取代理的版本
     */
    private String proxyVersion;

    /**
     * 查询是否缓存
     * @return
     */
    public boolean checkHttpProxyCache(){
        return  httpProxyCache != null;
    }

    /**
     * 根据appKey 查询订阅信息
     * @param proxyCode
     * @return
     */
    public HttpProxyInfo getHttpProxy(String proxyCode){
        return httpProxyCache.get(proxyCode);
    }

    /**
     * 获取应用授权信息
     */
    public void refreshCache(GatewayProxySyncInfo proxySyncInfo){
        synchronized (this){
            if (proxySyncInfo != null
                    && proxySyncInfo.getProxyVersion() != null
                    && !proxySyncInfo.getProxyVersion().equals(this.proxyVersion)){

                Map<String,HttpProxyInfo> newMap =
                        proxySyncInfo.getProxyInfos()
                                .stream()
                                .collect(Collectors.toMap(HttpProxyInfo::getProxyCode,
                                        Function.identity(),
                                        (k1, k2) -> k2));
                Set<String> newKeys = newMap.keySet();
                Set<String> odlKeys = httpProxyCache.keySet();
                //清理
                odlKeys.forEach(oldKey -> {
                    if (!newKeys.contains(oldKey)){
                        httpProxyCache.remove(oldKey);
                    }
                });
                //更新
                newKeys.forEach(newKey -> {
                   httpProxyCache.put(newKey,newMap.get(newKey));
                });
                this.proxyVersion = proxySyncInfo.getProxyVersion();
            }
        }
    }

    public String getProxyVersion() {
        return proxyVersion;
    }
}
