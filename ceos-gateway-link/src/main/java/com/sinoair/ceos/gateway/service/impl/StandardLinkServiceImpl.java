package com.sinoair.ceos.gateway.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import com.sinoair.ceos.gateway.cache.GatewayCache;
import com.sinoair.ceos.gateway.common.constant.GatewayConstant;
import com.sinoair.ceos.gateway.common.dto.api.ApiResult;
import com.sinoair.ceos.gateway.common.dto.api.HttpResultDto;
import com.sinoair.ceos.gateway.common.dto.api.RpcResult;
import com.sinoair.ceos.gateway.common.dto.app.AppInfo;
import com.sinoair.ceos.gateway.common.dto.app.AppResource;
import com.sinoair.ceos.gateway.common.exception.CeosGatewayCallException;
import com.sinoair.ceos.gateway.common.exception.CeosGatewayException;
import com.sinoair.ceos.gateway.common.util.ApiResultBuilder;
import com.sinoair.ceos.gateway.common.util.HttpUtil;
import com.sinoair.ceos.gateway.common.util.SignatureUtil;
import com.sinoair.ceos.gateway.common.util.StringUtil;
import com.sinoair.ceos.gateway.domain.constant.LinkStatus;
import com.sinoair.ceos.gateway.service.StandardLinkService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: daxiong
 */
@Service
public class StandardLinkServiceImpl implements StandardLinkService {

    @Autowired
    private GatewayCache gatewayCache;

    @Autowired
    @Qualifier("RedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;


    @Autowired
    private DefaultRedisScript<Long> redisRateLimitScript;

    @Value(("${spring.application.name}"))
    private String applicationName;

    /**
     * 处理并转发
     *
     * @param request
     * @return
     */
    @Override
    public RpcResult rpcLink(HttpServletRequest request, String messageId,String requestBody) {

        // 请求方时间戳 毫秒时间戳
        String timestamp = request.getHeader("Timestamp");

        // 资源code
        String resourceCode = request.getHeader("Resource-Code");

        // appKey 接入id
        String appKey = request.getHeader("App-Key");

        // 签名
        String signature = request.getHeader("Signature");

        // 校验应用信息，AppKey和资源code校验
        AppResource appResource = this.checkAppInfo(appKey, resourceCode);
        // 读取缓存信息
        AppInfo appInfo = this.gatewayCache.getAppInfo(appKey);

        if (1 == appResource.getSwitchTimestamp()){
            // 时间戳校验
            this.checkTimestamp(timestamp);
        }
        if (1 == appResource.getSwitchRequestBody()){
            // 请求体json校验
            this.checkRequestBody(requestBody);
        }
        if (1 == appResource.getSwitchSignature()){
            this.checkSign(signature,appKey,appInfo.getAppSecret(),resourceCode,requestBody,Long.valueOf(timestamp));
        }
        // 全局幂等检查
        if (1 == appResource.getSwitchIdempotent()){
            this.checkIdempotent(requestBody,appResource,appKey);
        }
        // 限流保护
        switch (appResource.getSwitchRateLimit()){
            case 1:
                RateLimiter rateLimiter = gatewayCache.getLimiter(appInfo.getAppKey(),appResource.getResCode());
                if(rateLimiter != null){
                    if (!rateLimiter.tryAcquire()){
                        throw new CeosGatewayException(LinkStatus.ERROR_MAX_VISIT.getMessage(),LinkStatus.ERROR_MAX_VISIT.getCode());
                    }
                }
                break;
            case 2:
                String key = GatewayConstant.REDIS_TEMPLATE_PREFIX+appInfo.getAppKey()+":"+appResource.getResCode();
                List<String> keys = Collections.singletonList(key);

                Long number = redisTemplate.execute(redisRateLimitScript, keys,appResource.getRateLimit(), 1);
                if (number != null && number != 0 && number < appResource.getRateLimit() ){
                    // 已经获取到令牌
                    // 继续执行
                }else{
                    throw new CeosGatewayException(LinkStatus.ERROR_MAX_VISIT.getMessage(),LinkStatus.ERROR_MAX_VISIT.getCode());
                }
                break;
            default://不限流
        }
        // 远程调用
        Map<String, String> header = new HashMap<>();
        header.put("Timestamp",timestamp);
        header.put("Resource-Code",resourceCode);
        header.put("App-Key",appKey);
        header.put("Signature",signature);
        header.put("Gateway-Source", this.applicationName);
        header.put("Gateway-Version", this.gatewayCache.getGatewayVersion());
        header.put("Gateway-Message-ID", messageId);
        try {
            String appName = URLEncoder.encode(appInfo.getAppName(), "UTF-8");
            header.put("App-Name", appName);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        RpcResult rpc = this.rpc(appResource, header, requestBody);
        rpc.setAppInfo(appInfo);
        return rpc;
    }

    /**
     * 释放幂等锁
     *
     * @param requestBody
     * @param appResource
     * @param appKey
     */
    @Override
    public void unlockIdempotent(String requestBody, AppResource appResource, String appKey) {
        if (appResource != null && 1 == appResource.getSwitchIdempotent()){
            // 获取请求的hash值
            String sha1 = DigestUtils.sha1Hex(requestBody);
            String val = DigestUtils.md5Hex(requestBody+sha1);
            // 绑定key
            String key = GatewayConstant.REDIS_IDEMPOTENT_LOCK_PREFIX + DigestUtils.md5Hex(appKey)+":"+val;
            if (val.equals(redisTemplate.opsForValue().get(key))){
                redisTemplate.delete(key);
            }
        }
    }

    /**
     * 获取资源信息
     *
     * @param request
     * @return
     */
    @Override
    public AppResource getAppResource(HttpServletRequest request) {
        // 资源code
        String resourceCode = request.getHeader("Resource-Code");
        // appKey 接入id
        String appKey = request.getHeader("App-Key");
        if (StringUtil.isEmpty(resourceCode) || StringUtil.isEmpty(appKey)){
            return null;
        }else {
            AppInfo appInfo = this.gatewayCache.getAppInfo(appKey);
            if (appInfo == null){
                return null;
            }
            List<AppResource> appResources = appInfo.getAppResources();
            AppResource appResource = null;
            for (AppResource resource : appResources){
                if (resourceCode.equals(resource.getResCode())){
                    appResource = resource;
                    break;
                }
            }
            return appResource;
        }
    }

    /**
     * 时间戳校验
     * @param timestamp
     */
    private void checkTimestamp(String timestamp){
        if (timestamp == null){
            throw new CeosGatewayException(LinkStatus.ERROR_TIMESTAMP.getMessage(),LinkStatus.ERROR_TIMESTAMP.getCode());
        }
        long t = Long.valueOf(timestamp);
        long now = System.currentTimeMillis();
        if (t < now - 600_000L || t > now + 600_000L){
            throw new CeosGatewayException(LinkStatus.ERROR_TIMESTAMP.getMessage(),LinkStatus.ERROR_TIMESTAMP.getCode());
        }
    }

    /**
     * 校验订阅信息
     * @param appKey
     */
    private AppResource checkAppInfo(String appKey,String resourceCode){
        if (appKey == null){
            throw new CeosGatewayException(LinkStatus.ERROR_APP_KEY.getMessage(),LinkStatus.ERROR_APP_KEY.getCode());
        }
        if (resourceCode == null){
            throw new CeosGatewayException(LinkStatus.ERROR_RESOURCES_CODE.getMessage(),LinkStatus.ERROR_RESOURCES_CODE.getCode());
        }
        AppInfo appInfo = gatewayCache.getAppInfo(appKey);
        if (appInfo == null){
            throw new CeosGatewayException(LinkStatus.ERROR_APP_KEY.getMessage(),LinkStatus.ERROR_APP_KEY.getCode());
        }
        List<AppResource> appResources = appInfo.getAppResources();
        AppResource appResource = null;
        for (AppResource resource : appResources){
            if (resourceCode.equals(resource.getResCode())){
                appResource = resource;
                break;
            }
        }
        if (appResource == null || appResource.getResStatus() != 1){
            throw new CeosGatewayException(LinkStatus.ERROR_RESOURCES_CODE.getMessage(),LinkStatus.ERROR_RESOURCES_CODE.getCode());
        }
        return appResource;
    }

    /**
     * 校验请求体
     * @param requestBody
     */
    private void checkRequestBody(String requestBody){
        if (StringUtil.isEmpty(requestBody)){
            throw new CeosGatewayException(LinkStatus.ERROR_JSON.getMessage(), LinkStatus.ERROR_JSON.getCode());
        }
        try {
            if (!JSON.isValid(requestBody)){
                throw new CeosGatewayException(LinkStatus.ERROR_JSON.getMessage(), LinkStatus.ERROR_JSON.getCode());
            }
        }catch (Exception e){
            throw new CeosGatewayException(LinkStatus.ERROR_JSON.getMessage(), LinkStatus.ERROR_JSON.getCode());
        }
    }

    /**
     * 签名校验
     * @param signature
     * @param appKey
     * @param appSecret
     * @param resourceCode
     * @param requestBody
     */
    private void checkSign(String signature,String appKey,String appSecret,String resourceCode,String requestBody,Long timestamp){
        if (!SignatureUtil.checkSign(signature,appKey,appSecret,resourceCode,requestBody,timestamp)){
            throw new CeosGatewayException(LinkStatus.ERROR_SIGNATURE.getMessage(), LinkStatus.ERROR_SIGNATURE.getCode());
        }
    }

    /**
     * 幂等检查
     * @param requestBody
     * @param appResource
     * @param appKey
     */
    private void checkIdempotent(String requestBody, AppResource appResource,String appKey){
        // 获取请求的hash值
        String sha1 = DigestUtils.sha1Hex(requestBody);
        String val = DigestUtils.md5Hex(requestBody+sha1);
        // 绑定key
        String key = GatewayConstant.REDIS_IDEMPOTENT_LOCK_PREFIX + DigestUtils.md5Hex(appKey)+":"+val;
        Boolean a = redisTemplate.opsForValue().setIfAbsent(key, val, Duration.ofSeconds(appResource.getIdempotentTimeOut()));
        if (a){
            //获取锁成功
            //执行业务逻辑
        }else{
            //获取锁失败
            //快速失败,响应给客户端
            throw new CeosGatewayException(LinkStatus.ERROR_IDEMPOTENT.getMessage(), LinkStatus.ERROR_IDEMPOTENT.getCode());
        }
    }

    /**
     * 封装参数，远程调用
     * @param appResource
     * @param header
     * @param requestBody
     * @return
     */
    private RpcResult rpc(AppResource appResource, Map<String,String> header, String requestBody){
        if (appResource.getResHeader() != null){
            for(String key : appResource.getResHeader().keySet()){
                if (!header.containsKey(key)){
                    header.put(key,appResource.getResHeader().get(key));
                }
            }
        }
        HttpResultDto httpResultDto = null;
        try {
            httpResultDto = HttpUtil.post(appResource.getResUrl(), header, appResource.getResCookie(), appResource.getResFormData(), requestBody, appResource.getTimeout());
        }catch (CeosGatewayCallException e){
            e.setAppResource(appResource);
            appResource.getResApp();
            throw e;
        }
        JSONObject jsonObject = JSON.parseObject(httpResultDto.getResponseData());
        ApiResult apiResult = ApiResultBuilder.createBuilder()
                .code(jsonObject.getString("code"))
                .message(jsonObject.getString("message"))
                .result(jsonObject.get("result"))
                .newApiResult();
        return new RpcResult(apiResult,appResource,header,httpResultDto.getResponseHeader());
    }
}
