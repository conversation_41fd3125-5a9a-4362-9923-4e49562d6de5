spring:
  application:
    name: ceos-gateway-link
  redis:
    host: **************
    port: 36379
    password: kj_WY_fzplm@2021
    # 连接超时时间 30秒
    timeout: 30s
    database: 5
    jedis:
      pool:
        # 连接池最大连接数
        max-active: 100
        # 连接池最大阻塞时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
  data:
    mongodb:
      auto-index-creation: false
      # 连接副本集，slaveOk=true 表示开启副本节点的读支持，可实现读写分离，connect=replicaSet 表示自动到副本集中选择读写的主机，replicaSet=myrs 用来指定副本集的名称
      # uri: ***********************************************************************************************
      uri: *****************************************************************************************************
ceos-gateway:
  console:
    app-key: MjE5NjViNGRiMWRhZWZhNTYwOGM3MWQ2YmRmZjU2MTQ=
    app-secret: ZTc0ZWM5ZTE1NWE5MTZkZTlmMjJjZDAzMTJiMjM1ZmE=
    api:
      app-info: http://172.30.197.116:9666/gateway-console/gateway/info
  link:
    # 存储类型 同步：sync、异步：async
    log-save-type: async

# mq信息配置
rocketmq:
  name-server: *************:9876;*************:9876;*************:9876
  producer:
    # 发送消息超时时间，默认 3000
    sendMessageTimeout: 3000
    # 发送消息失败重试次数，默认2
    retryTimesWhenSendFailed: 2
    group: ceos-gateway