package com.sinoair.ceos.gateway.service.gateway.impl;

import com.alibaba.fastjson2.JSON;
import com.sinoair.ceos.gateway.common.dto.app.GatewaySyncInfoDto;
import com.sinoair.ceos.gateway.service.gateway.GatewayInfoService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Author: daxiong
 */
@SpringBootTest
class GatewayInfoServiceImplTest {

    @Autowired
    private GatewayInfoService gatewayInfoService;

    @Test
    void selectAppInfo() {

        GatewaySyncInfoDto gatewaySyncInfoDto = gatewayInfoService.selectAppInfo("123467");
        System.out.println(JSON.toJSONString(gatewaySyncInfoDto));

    }
}
