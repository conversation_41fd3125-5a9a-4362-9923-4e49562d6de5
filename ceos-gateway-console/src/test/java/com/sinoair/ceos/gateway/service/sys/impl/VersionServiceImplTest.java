package com.sinoair.ceos.gateway.service.sys.impl;

import com.sinoair.ceos.gateway.service.sys.VersionService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @author: 大雄
 */
@SpringBootTest
class VersionServiceImplTest {

    @Autowired
    private VersionService versionService;

    @Test
    void updateVersion() {
        versionService.updateVersion("大雄","测试");
    }
}
