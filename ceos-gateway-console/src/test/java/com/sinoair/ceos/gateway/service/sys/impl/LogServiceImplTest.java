package com.sinoair.ceos.gateway.service.sys.impl;

import com.sinoair.ceos.gateway.common.mongo.GatewayApiLogEntity;
import com.sinoair.ceos.gateway.domain.dto.log.ApiLogParamDto;
import com.sinoair.ceos.gateway.service.sys.LogService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * @Author: daxiong
 */
@SpringBootTest
class LogServiceImplTest {

    @Autowired
    private LogService logService;

    @Test
    void selectApiLog() {
        ApiLogParamDto apiLogParamDto = new ApiLogParamDto();
        //apiLogParamDto.setMessageId("fe0851b2-58b8-4c6c-813d-687f02f74ae4");
        apiLogParamDto.setSearchCode("123456");
        List<GatewayApiLogEntity> gatewayApiLogEntityList = logService.selectApiLog(null,"123456",null);
        System.out.println(gatewayApiLogEntityList.size());
    }
}
