package com.sinoair.ceos.gateway.service.sys.impl;

import com.alibaba.fastjson2.JSON;
import com.sinoair.ceos.gateway.domain.dto.sys.ResListParam;
import com.sinoair.ceos.gateway.domain.model.GwResource;
import com.sinoair.ceos.gateway.service.sys.ResourceService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * @author: 大雄
 */
@SpringBootTest
class ResourceServiceImplTest {

    @Autowired
    private ResourceService resourceService;

    @Test
    void selectListByParam() {
        ResListParam resListParam = new ResListParam();
        resListParam.setSearch("CEOS_ORDER_CREATE");
        List<GwResource> gwResources = resourceService.selectListByParam(resListParam);
        System.out.println(JSON.toJSONString(gwResources));
    }
}
