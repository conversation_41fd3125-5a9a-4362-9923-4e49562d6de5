package com.sinoair.ceos.gateway.service.sys.impl;

import com.sinoair.ceos.gateway.domain.dto.sys.AppListParam;
import com.sinoair.ceos.gateway.domain.dto.sys.AppPermission;
import com.sinoair.ceos.gateway.domain.dto.sys.AppPermissionListParam;
import com.sinoair.ceos.gateway.domain.model.GwAppInfo;
import com.sinoair.ceos.gateway.service.sys.AppService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * @author: 大雄
 */
@SpringBootTest
class AppServiceImplTest {

    @Autowired
    private AppService appService;

    @Test
    void selectListByParam() {

        AppListParam appListParam = new AppListParam();
        //appListParam.setAppName("测试");
        List<GwAppInfo> gwAppInfos = appService.selectListByParam(appListParam);
        assert(gwAppInfos.size() >0);

    }

    @Test
    void selectAppResourcesList() {
        AppPermissionListParam appPermissionListParam = new AppPermissionListParam();
        appPermissionListParam.setAppKey("ZWFkMjM3YjdkOGE4OGZiMWFmMWVkMmZmNjFiZTg2YmI=");
        List<AppPermission> gwAppInfos = appService.selectAppResourcesList(appPermissionListParam);
        assert(gwAppInfos.size() >0);
    }
}
