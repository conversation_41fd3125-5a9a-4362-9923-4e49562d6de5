package com.sinoair.ceos.gateway.service.gateway.impl;

import com.alibaba.fastjson2.JSON;
import com.sinoair.ceos.gateway.common.dto.proxy.GatewayProxySyncInfo;
import com.sinoair.ceos.gateway.service.gateway.GatewayProxyService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @author: 大雄
 */
@SpringBootTest
class GatewayProxyServiceImplTest {

    @Autowired
    private GatewayProxyService gatewayProxyService;

    @Test
    void selectProxyInfo() {

        GatewayProxySyncInfo gatewayProxySyncInfo = gatewayProxyService.selectProxyInfo(null);

        System.out.println(JSON.toJSONString(gatewayProxySyncInfo));

    }
}
