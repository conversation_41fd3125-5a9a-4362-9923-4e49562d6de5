# 数据库驱动jar 硬盘路径
drive.class.path=D:\\tools\\maven\\repository\\mysql\\mysql-connector-java\\8.0.27\\mysql-connector-java-8.0.27.jar
# 数据库连接参数
jdbc.driver= com.mysql.cj.jdbc.Driver
jdbc.url=****************************************************************************************************************
jdbc.username=ceos_gateway
jdbc.password=CeosGateway@3.190

# 包路径配置
model.package=com.sinoair.ceos.gateway.domain.model
dao.package=com.sinoair.ceos.gateway.dao
tk.mapper.class=com.sinoair.ceos.gateway.core.config.MyMapper
