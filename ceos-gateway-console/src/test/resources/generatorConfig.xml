<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <!-- 引入数据库连接配置 -->
    <properties url="${mybatis.generator.generatorConfig.properties}"/>

    <context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!-- 配置 tk.mybatis 插件 -->
        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="${tk.mapper.class}"/>
        </plugin>

        <!-- 配置数据库连接 -->
        <jdbcConnection
                driverClass="${jdbc.driver}"
                connectionURL="${jdbc.url}"
                userId="${jdbc.username}"
                password="${jdbc.password}">
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>

        <!-- 配置实体类存放路径 -->
        <javaModelGenerator targetPackage="${model.package}" targetProject="src/test/java"/>

        <!-- 配置 XML 存放路径 -->
        <sqlMapGenerator targetPackage="mapper" targetProject="src/test/resources"/>

        <!-- 配置 DAO 存放路径 -->
        <javaClientGenerator
                targetPackage="${dao.package}"
                targetProject="src/test/java"
                type="XMLMAPPER"/>

        <!-- 配置需要指定生成的数据库和表，% 代表所有表 -->
        <!-- <table tableName="%">
             &lt;!&ndash; mysql 配置 &ndash;&gt;
             <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
         </table>-->

        <!-- <table tableName="line_bag">
             <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
         </table>
         <table tableName="line_bag_parcel"></table>
         <table tableName="line_instruction_config">
             <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
         </table>
         <table tableName="line_parcel">
             <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
         </table>
         <table tableName="line_route">
             <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
         </table>-->
        <!--<table tableName="%">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>-->

        <table tableName="gw_proxy">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>
        <table tableName="gw_proxy_version">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>
    </context>
</generatorConfiguration>
