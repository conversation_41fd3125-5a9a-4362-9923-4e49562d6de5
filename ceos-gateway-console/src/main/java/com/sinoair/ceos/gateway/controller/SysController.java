package com.sinoair.ceos.gateway.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sinoair.ceos.gateway.common.dto.antd.table.AntTablePage;
import com.sinoair.ceos.gateway.common.dto.api.ApiResult;
import com.sinoair.ceos.gateway.common.exception.CeosGatewayException;
import com.sinoair.ceos.gateway.common.util.ApiResultBuilder;
import com.sinoair.ceos.gateway.common.util.GatewayValidateUtil;
import com.sinoair.ceos.gateway.domain.dto.sys.*;
import com.sinoair.ceos.gateway.domain.model.GwAppInfo;
import com.sinoair.ceos.gateway.domain.model.GwDict;
import com.sinoair.ceos.gateway.domain.model.GwProxy;
import com.sinoair.ceos.gateway.domain.model.GwResource;
import com.sinoair.ceos.gateway.service.gateway.GatewayProxyService;
import com.sinoair.ceos.gateway.service.sys.AppService;
import com.sinoair.ceos.gateway.service.sys.DictService;
import com.sinoair.ceos.gateway.service.sys.ResourceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

/**
 * @Author: daxiong
 */
@RestController
@RequestMapping("/sys")
public class SysController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SysController.class);

    @Value("${ceos-gateway.secret.devops.app-key}")
    private String devOpsAppKey;
    @Value("${ceos-gateway.secret.devops.app-secret}")
    private String devOpsAppSecret;
    @Value("${server.servlet.context-path}")
    private String contextPath;

    @Autowired
    private AppService appService;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private DictService dictService;

    @Autowired
    private GatewayProxyService gatewayProxyService;

    /**
     * 应用管理
     * @param request
     * @return
     */
    @PostMapping("/app/list")
    public ResponseEntity appList(HttpServletRequest request) {
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        try {
            String requestParam = GatewayValidateUtil.readRequestParam(request, devOpsAppKey, devOpsAppSecret, contextPath + "/sys/app/list");
            AppListParam appListParam = JSON.parseObject(requestParam, AppListParam.class);

            // 分页器
            PageHelper.startPage(appListParam.getCurrent(), appListParam.getPageSize());
            List<GwAppInfo> gwAppInfos = this.appService.selectListByParam(appListParam);
            PageInfo<GwAppInfo> page = new PageInfo<>(gwAppInfos);
            AntTablePage<GwAppInfo> tablePage = new AntTablePage<>();
            // 当前页表格数据信息
            tablePage.setData(page.getList());
            // 总数量
            tablePage.setTotalCount(page.getTotal());
            // 第几页
            tablePage.setCurrent(appListParam.getCurrent());
            // 每页多少条
            tablePage.setPageSize(appListParam.getPageSize());
            // 总页数
            tablePage.setTotalPage(page.getPages());
            apiResult = ApiResultBuilder.createBuilder()
                    .success(tablePage)
                    .newApiResult();
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code("-1")
                        .message("服务器异常，请稍候再试")
                        .newApiResult();
            }
            e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }finally {
            PageHelper.clearPage();
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }
    /**
     * 新增或者更新应用
     * @param request
     * @return
     */
    @PostMapping("/app/save")
    public ResponseEntity appSave(HttpServletRequest request){
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        try {
            String requestParam = GatewayValidateUtil.readRequestParam(request, devOpsAppKey, devOpsAppSecret, contextPath + "/sys/app/save");
            AppSaveParam appListParam = JSON.parseObject(requestParam, AppSaveParam.class);
            GwAppInfo gwAppInfo = this.appService.saveAppInfo(appListParam);
            apiResult = ApiResultBuilder.createBuilder().success(gwAppInfo).newApiResult();
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code("-1")
                        .message("服务器异常，请稍候再试")
                        .newApiResult();
            }
            e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }

    /**
     * 重置应用密钥
     * @param request
     * @return
     */
    @PostMapping("/app/secret/reset")
    public ResponseEntity appSecretReset(HttpServletRequest request){
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        try {
            String requestParam = GatewayValidateUtil.readRequestParam(request, devOpsAppKey, devOpsAppSecret, contextPath + "/sys/app/secret/reset");
            AppSecretResetParam appListParam = JSON.parseObject(requestParam, AppSecretResetParam.class);
            String s = this.appService.resetSecret(appListParam);
            apiResult = ApiResultBuilder.createBuilder().success(s).newApiResult();
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code("-1")
                        .message("服务器异常，请稍候再试")
                        .newApiResult();
            }
            e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }

    /**
     * API资源类别
     * @param request
     * @return
     */
    @PostMapping("/app/res/list")
    public ResponseEntity appResList(HttpServletRequest request){
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        try {
            String requestParam = GatewayValidateUtil.readRequestParam(request, devOpsAppKey, devOpsAppSecret, contextPath + "/sys/app/res/list");
            AppPermissionListParam appPermissionListParam = JSON.parseObject(requestParam, AppPermissionListParam.class);

            // 分页器
            PageHelper.startPage(appPermissionListParam.getCurrent(), appPermissionListParam.getPageSize());
            List<AppPermission> appPermissions = this.appService.selectAppResourcesList(appPermissionListParam);
            PageInfo<AppPermission> page = new PageInfo<>(appPermissions);
            AntTablePage<AppPermission> tablePage = new AntTablePage<>();
            // 当前页表格数据信息
            tablePage.setData(page.getList());
            // 总数量
            tablePage.setTotalCount(page.getTotal());
            // 第几页
            tablePage.setCurrent(appPermissionListParam.getCurrent());
            // 每页多少条
            tablePage.setPageSize(appPermissionListParam.getPageSize());
            // 总页数
            tablePage.setTotalPage(page.getPages());
            apiResult = ApiResultBuilder.createBuilder()
                    .success(tablePage)
                    .newApiResult();
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code("-1")
                        .message("服务器异常，请稍候再试")
                        .newApiResult();
            }
            e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }finally {
            PageHelper.clearPage();
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }

    /**
     * 应用资源更新
     * @param request
     * @return
     */
    @PostMapping("/app/res/save")
    public ResponseEntity appResSave(HttpServletRequest request){
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        try {
            String requestParam = GatewayValidateUtil.readRequestParam(request, devOpsAppKey, devOpsAppSecret, contextPath + "/sys/app/res/save");
            AppPermissioinSaveParam appPermissioinSaveParam = JSON.parseObject(requestParam, AppPermissioinSaveParam.class);
            this.appService.saveAppPermission(appPermissioinSaveParam);
            apiResult = ApiResultBuilder.createBuilder().success(null).newApiResult();
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code("-1")
                        .message("服务器异常，请稍候再试")
                        .newApiResult();
            }
            e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }

    /**
     * 资源列表
     * @param request
     * @return
     */
    @PostMapping("/res/list")
    public ResponseEntity resList(HttpServletRequest request){
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        try {
            String requestParam = GatewayValidateUtil.readRequestParam(request, devOpsAppKey, devOpsAppSecret, contextPath + "/sys/res/list");
            ResListParam resListParam = JSON.parseObject(requestParam, ResListParam.class);

            // 分页器
            PageHelper.startPage(resListParam.getCurrent(), resListParam.getPageSize());
            List<GwResource> resources = this.resourceService.selectListByParam(resListParam);
            PageInfo<GwResource> page = new PageInfo<>(resources);
            AntTablePage<GwResource> tablePage = new AntTablePage<>();
            // 当前页表格数据信息
            tablePage.setData(page.getList());
            // 总数量
            tablePage.setTotalCount(page.getTotal());
            // 第几页
            tablePage.setCurrent(resListParam.getCurrent());
            // 每页多少条
            tablePage.setPageSize(resListParam.getPageSize());
            // 总页数
            tablePage.setTotalPage(page.getPages());
            apiResult = ApiResultBuilder.createBuilder()
                    .success(tablePage)
                    .newApiResult();
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code("-1")
                        .message("服务器异常，请稍候再试")
                        .newApiResult();
            }
            e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }finally {
            PageHelper.clearPage();
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }

    /**
     * 保存资源信息
     * @param request
     * @return
     */
    @PostMapping("/res/save")
    public ResponseEntity resSave(HttpServletRequest request) {
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        try {
            String requestParam = GatewayValidateUtil.readRequestParam(request, devOpsAppKey, devOpsAppSecret, contextPath + "/sys/res/save");
            ResSaveParam resSaveParam = JSON.parseObject(requestParam, ResSaveParam.class);
            GwResource save = this.resourceService.save(resSaveParam);
            apiResult = ApiResultBuilder.createBuilder().success(save).newApiResult();
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code("-1")
                        .message("服务器异常，请稍候再试")
                        .newApiResult();
            }
            e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }

    /**
     * 查询字典数据
     * @param request
     * @return
     */
    @PostMapping("/dict/search")
    public ResponseEntity dict(HttpServletRequest request) {
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        try {
            String requestParam = GatewayValidateUtil.readRequestParam(request, devOpsAppKey, devOpsAppSecret, contextPath + "/sys/dict/search");
            JSONObject jo = JSON.parseObject(requestParam);
            if (jo != null && jo.getString("groupCode") != null){
                GwDict groupCode = dictService.selectByGroup(jo.getString("groupCode"));
                apiResult = ApiResultBuilder.createBuilder()
                        .success(groupCode)
                        .newApiResult();
            }else{
                apiResult = ApiResultBuilder.createBuilder()
                        .success(new HashMap<>())
                        .newApiResult();
            }
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code("-1")
                        .message("服务器异常，请稍候再试")
                        .newApiResult();
            }
            e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }finally {
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }

    /**
     * 资源列表,未分配
     * @param request
     * @return
     */
    @PostMapping("/res/list/permission/not")
    public ResponseEntity resNotPermissionList(HttpServletRequest request){
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        try {
            String requestParam = GatewayValidateUtil.readRequestParam(request, devOpsAppKey, devOpsAppSecret, contextPath + "/sys/res/list/permission/not");
            JSONObject jsonObject = JSON.parseObject(requestParam);
            List<GwResource> resources = this.resourceService.selectListNotPermission(jsonObject.getString("appKey"));
            apiResult = ApiResultBuilder.createBuilder()
                    .success(resources)
                    .newApiResult();
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code("-1")
                        .message("服务器异常，请稍候再试")
                        .newApiResult();
            }
            e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }finally {
            PageHelper.clearPage();
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }

    /**
     * 获取代理列表
     * @param request
     * @return
     */
    @PostMapping("/proxy/list")
    public ResponseEntity proxyList(HttpServletRequest request){
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        try {
            String requestParam = GatewayValidateUtil.readRequestParam(request, devOpsAppKey, devOpsAppSecret, contextPath + "/sys/proxy/list");
            ProxyListParam proxyListParam = JSON.parseObject(requestParam, ProxyListParam.class);

            // 分页器
            PageHelper.startPage(proxyListParam.getCurrent(), proxyListParam.getPageSize());
            List<GwProxy> gwProxies = this.gatewayProxyService.selectProxyList(proxyListParam);
            PageInfo<GwProxy> page = new PageInfo<>(gwProxies);
            AntTablePage<GwProxy> tablePage = new AntTablePage<>();
            // 当前页表格数据信息
            tablePage.setData(page.getList());
            // 总数量
            tablePage.setTotalCount(page.getTotal());
            // 第几页
            tablePage.setCurrent(proxyListParam.getCurrent());
            // 每页多少条
            tablePage.setPageSize(proxyListParam.getPageSize());
            // 总页数
            tablePage.setTotalPage(page.getPages());
            apiResult = ApiResultBuilder.createBuilder()
                    .success(tablePage)
                    .newApiResult();
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code("-1")
                        .message("服务器异常，请稍候再试")
                        .newApiResult();
            }
            e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }finally {
            PageHelper.clearPage();
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }

    /**
     * 获取代理保存
     * @param request
     * @return
     */
    @PostMapping("/proxy/save")
    public ResponseEntity proxySave(HttpServletRequest request){
        String messageId = UUID.randomUUID().toString();
        ApiResult apiResult = null;
        try {
            String requestParam = GatewayValidateUtil.readRequestParam(request, devOpsAppKey, devOpsAppSecret, contextPath + "/sys/proxy/save");
            ProxySaveParam proxySaveParam = JSON.parseObject(requestParam, ProxySaveParam.class);
            GwProxy gwProxy = this.gatewayProxyService.saveProxy(proxySaveParam);
            apiResult = ApiResultBuilder.createBuilder().success(gwProxy).newApiResult();
        }catch (Exception e){
            if (e instanceof CeosGatewayException){
                // 服务异常
                CeosGatewayException e1 = (CeosGatewayException) e;
                apiResult = ApiResultBuilder.createBuilder()
                        .code(e1.getCode())
                        .message(e1.getMessage())
                        .messageId(messageId)
                        .newApiResult();
            }else{
                // 远程调用异常
                apiResult = ApiResultBuilder.createBuilder()
                        .messageId(messageId)
                        .code("-1")
                        .message("服务器异常，请稍候再试")
                        .newApiResult();
            }
            e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }
}
