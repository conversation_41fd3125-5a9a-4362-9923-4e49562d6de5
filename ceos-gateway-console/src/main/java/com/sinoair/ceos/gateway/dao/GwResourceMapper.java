package com.sinoair.ceos.gateway.dao;

import com.sinoair.ceos.gateway.core.config.MyMapper;
import com.sinoair.ceos.gateway.domain.dto.sys.ResListParam;
import com.sinoair.ceos.gateway.domain.model.GwResource;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GwResourceMapper extends MyMapper<GwResource> {

    /**
     * 查询资源列表
     * @param param
     * @return
     */
    List<GwResource> selectTableList(@Param("param") ResListParam param);

    /**
     * 根据资源Code
     * @param resCode
     * @return
     */
    GwResource selectByResCode(@Param("resCode") String resCode);

    /**
     * 查询未分配的资源
     * @param appKey
     * @return
     */
    List<GwResource> selectListNotPermission(@Param("appKey")String appKey);

}
