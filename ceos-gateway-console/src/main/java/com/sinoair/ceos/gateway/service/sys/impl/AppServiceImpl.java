package com.sinoair.ceos.gateway.service.sys.impl;

import com.alibaba.fastjson2.JSON;
import com.sinoair.ceos.gateway.common.exception.CeosGatewayException;
import com.sinoair.ceos.gateway.common.util.StringUtil;
import com.sinoair.ceos.gateway.core.utils.AppUtil;
import com.sinoair.ceos.gateway.dao.GwAppInfoMapper;
import com.sinoair.ceos.gateway.dao.GwAppPermissionMapper;
import com.sinoair.ceos.gateway.domain.dto.sys.*;
import com.sinoair.ceos.gateway.domain.model.GwAppInfo;
import com.sinoair.ceos.gateway.domain.model.GwAppPermission;
import com.sinoair.ceos.gateway.service.sys.AppService;
import com.sinoair.ceos.gateway.service.sys.VersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @Author: daxiong
 */
@Service
public class AppServiceImpl implements AppService {

    @Autowired
    private GwAppPermissionMapper gwAppPermissionMapper;

    @Autowired
    private GwAppInfoMapper gwAppInfoMapper;

    @Autowired
    private VersionService versionService;

    /**
     * 查询应用信息
     *
     * @param appListParam
     * @return
     */
    @Override
    public List<GwAppInfo> selectListByParam(AppListParam appListParam) {
        return this.gwAppInfoMapper.selectListByParam(appListParam);
    }

    /**
     * 保存应用信息
     *
     * @param appSaveParam
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public GwAppInfo saveAppInfo(AppSaveParam appSaveParam) {
        if (StringUtil.isEmpty(appSaveParam.getAppName())){
            throw new CeosGatewayException("应用名称不能为空","-1");
        }
        GwAppInfo gwAppInfo = null;
        if ("add".equals(appSaveParam.getSaveType())){

            if (StringUtil.isEmpty(appSaveParam.getAppSecret())){
                appSaveParam.setAppSecret(AppUtil.createRandomInfo(appSaveParam.getAppName() + UUID.randomUUID().toString()));
            }

            if (StringUtil.isEmpty(appSaveParam.getAppKey())){
                appSaveParam.setAppKey(AppUtil.createRandomInfo(appSaveParam.getAppName()));
            }else{
                if (this.gwAppInfoMapper.selectByAppKey(appSaveParam.getAppKey()) != null){
                    throw new CeosGatewayException("AppKey已存在","-1");
                }
            }

            gwAppInfo = new GwAppInfo();
            // 新增
            gwAppInfo.setAppName(appSaveParam.getAppName());
            gwAppInfo.setAppKey(appSaveParam.getAppKey());
            gwAppInfo.setAppSecret(appSaveParam.getAppSecret());
            gwAppInfo.setAppDescription(appSaveParam.getAppDescription());
            gwAppInfo.setRemark(appSaveParam.getRemark());
            gwAppInfo.setAppStatus(appSaveParam.getAppStatus());
            Date now = new Date();
            gwAppInfo.setCreateTime(now);
            gwAppInfo.setHandler(appSaveParam.getHandler());
            this.gwAppInfoMapper.insert(gwAppInfo);
        }else{
            // 更新
            if (appSaveParam.getId() == null){
                throw new CeosGatewayException("应用ID不能为空","-1");
            }
            gwAppInfo = this.gwAppInfoMapper.selectByPrimaryKey(appSaveParam.getId());
            if ( gwAppInfo != null){
                gwAppInfo.setAppStatus(appSaveParam.getAppStatus());
                gwAppInfo.setAppName(appSaveParam.getAppName());
                gwAppInfo.setAppDescription(appSaveParam.getAppDescription());
                gwAppInfo.setRemark(appSaveParam.getRemark());
                Date now = new Date();
                gwAppInfo.setHandler(appSaveParam.getHandler());
                this.gwAppInfoMapper.updateByPrimaryKey(gwAppInfo);
            }
        }
        // 更新版本
        this.versionService.updateVersion(appSaveParam.getHandler(),"更新客户端应用信息");
        return gwAppInfo;
    }

    /**
     * 重置secret
     *
     * @param appSecretResetParam
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public String resetSecret(AppSecretResetParam appSecretResetParam) {
        GwAppInfo gwAppInfo = new GwAppInfo();
        gwAppInfo.setId(appSecretResetParam.getId());
        gwAppInfo.setAppSecret(AppUtil.createRandomInfo(appSecretResetParam.getAppKey()));
        gwAppInfo.setHandleTime(new Date());
        gwAppInfo.setHandler(appSecretResetParam.getHandler());
        this.gwAppInfoMapper.updateByPrimaryKeySelective(gwAppInfo);
        this.versionService.updateVersion(appSecretResetParam.getHandler(), "重置应用密钥");
        return gwAppInfo.getAppSecret();
    }

    /**
     * 查询应用绑定的api资源
     *
     * @param appPermissionParam
     * @return
     */
    @Override
    public List<AppPermission> selectAppResourcesList(AppPermissionListParam appPermissionParam) {
        return this.gwAppPermissionMapper.selectTableList(appPermissionParam);
    }

    /**
     * 保存授权信息
     *
     * @param appPermissioinSaveParam
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void saveAppPermission(AppPermissioinSaveParam appPermissioinSaveParam) {
        String saveType = appPermissioinSaveParam.getSaveType();
        GwAppPermission gwAppPermission = JSON.parseObject(JSON.toJSONString(appPermissioinSaveParam),GwAppPermission.class);
        switch (saveType){
            case "delete": {
                this.gwAppPermissionMapper.deleteByPrimaryKey(gwAppPermission.getId());
                // 更新版本
                this.versionService.updateVersion(appPermissioinSaveParam.getHandler(),"删除API授权");
                break;
            }
            case "add": {
                gwAppPermission.setCreateTime(new Date());
                gwAppPermission.setHandleTime(new Date());
                this.gwAppPermissionMapper.insert(gwAppPermission);
                // 更新版本
                this.versionService.updateVersion(appPermissioinSaveParam.getHandler(),"新增API授权");
                break;
            }
            case "update": {
                gwAppPermission.setHandleTime(new Date());
                this.gwAppPermissionMapper.updateByPrimaryKey(gwAppPermission);
                // 更新版本
                this.versionService.updateVersion(appPermissioinSaveParam.getHandler(), "更新API授权");
                break;
            }
            default:  throw new CeosGatewayException("Error saveType","-1");
        }
    }
}
