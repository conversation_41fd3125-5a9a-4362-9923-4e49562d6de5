package com.sinoair.ceos.gateway.domain.model;

import javax.persistence.*;
import java.util.Date;

/**
 * 表名：gw_proxy
*/
@Table(name = "gw_proxy")
public class GwProxy {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 唯一键
     */
    @Column(name = "proxy_uid")
    private String proxyUid;

    /**
     * 代理编码
     */
    @Column(name = "proxy_code")
    private String proxyCode;

    /**
     * 代理名称
     */
    @Column(name = "proxy_name")
    private String proxyName;

    /**
     * 反向代理：reverse
正向代理：origin
     */
    @Column(name = "proxy_type")
    private String proxyType;

    /**
     * 状态
     */
    @Column(name = "proxy_status")
    private Integer proxyStatus;

    /**
     * 目标url
     */
    @Column(name = "target_url")
    private String targetUrl;

    /**
     * 目标类型text, redirect
     */
    @Column(name = "target_type")
    private String targetType;

    /**
     * 请求方式: GET POST DELETE...
     */
    @Column(name = "target_method")
    private String targetMethod;

    /**
     * 超时时间
     */
    @Column(name = "target_timeout")
    private Integer targetTimeout;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     */
    @Column(name = "switch_rate_limit")
    private Integer switchRateLimit;

    /**
     * 限流速度 speed/s
     */
    @Column(name = "rate_limit")
    private Integer rateLimit;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "handle_time")
    private Date handleTime;

    /**
     * 更新人
     */
    private String handler;

    /**
     * 负责人
     */
    private String coder;

    /**
     * 是否开启log存储
     */
    @Column(name = "switch_log")
    private Integer switchLog;

    /**
     * 请求头列表
     */
    @Column(name = "target_headers")
    private String targetHeaders;

    /**
     * 请求参数列表
     */
    @Column(name = "target_forms")
    private String targetForms;

    /**
     * 预留元数据
     */
    @Column(name = "meta_data")
    private String metaData;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取唯一键
     *
     * @return proxyUid - 唯一键
     */
    public String getProxyUid() {
        return proxyUid;
    }

    /**
     * 设置唯一键
     *
     * @param proxyUid 唯一键
     */
    public void setProxyUid(String proxyUid) {
        this.proxyUid = proxyUid;
    }

    /**
     * 获取代理编码
     *
     * @return proxyCode - 代理编码
     */
    public String getProxyCode() {
        return proxyCode;
    }

    /**
     * 设置代理编码
     *
     * @param proxyCode 代理编码
     */
    public void setProxyCode(String proxyCode) {
        this.proxyCode = proxyCode;
    }

    /**
     * 获取代理名称
     *
     * @return proxyName - 代理名称
     */
    public String getProxyName() {
        return proxyName;
    }

    /**
     * 设置代理名称
     *
     * @param proxyName 代理名称
     */
    public void setProxyName(String proxyName) {
        this.proxyName = proxyName;
    }

    /**
     * 获取反向代理：reverse
正向代理：origin
     *
     * @return proxyType - 反向代理：reverse
正向代理：origin
     */
    public String getProxyType() {
        return proxyType;
    }

    /**
     * 设置反向代理：reverse
正向代理：origin
     *
     * @param proxyType 反向代理：reverse
正向代理：origin
     */
    public void setProxyType(String proxyType) {
        this.proxyType = proxyType;
    }

    /**
     * 获取状态
     *
     * @return proxyStatus - 状态
     */
    public Integer getProxyStatus() {
        return proxyStatus;
    }

    /**
     * 设置状态
     *
     * @param proxyStatus 状态
     */
    public void setProxyStatus(Integer proxyStatus) {
        this.proxyStatus = proxyStatus;
    }

    /**
     * 获取目标url
     *
     * @return targetUrl - 目标url
     */
    public String getTargetUrl() {
        return targetUrl;
    }

    /**
     * 设置目标url
     *
     * @param targetUrl 目标url
     */
    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    /**
     * 获取目标类型text, redirect
     *
     * @return targetType - 目标类型text, redirect
     */
    public String getTargetType() {
        return targetType;
    }

    /**
     * 设置目标类型text, redirect
     *
     * @param targetType 目标类型text, redirect
     */
    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    /**
     * 获取请求方式: GET POST DELETE...
     *
     * @return targetMethod - 请求方式: GET POST DELETE...
     */
    public String getTargetMethod() {
        return targetMethod;
    }

    /**
     * 设置请求方式: GET POST DELETE...
     *
     * @param targetMethod 请求方式: GET POST DELETE...
     */
    public void setTargetMethod(String targetMethod) {
        this.targetMethod = targetMethod;
    }

    /**
     * 获取超时时间
     *
     * @return targetTimeout - 超时时间
     */
    public Integer getTargetTimeout() {
        return targetTimeout;
    }

    /**
     * 设置超时时间
     *
     * @param targetTimeout 超时时间
     */
    public void setTargetTimeout(Integer targetTimeout) {
        this.targetTimeout = targetTimeout;
    }

    /**
     * 获取备注
     *
     * @return remark - 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     *
     * @return switchRateLimit - 是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     */
    public Integer getSwitchRateLimit() {
        return switchRateLimit;
    }

    /**
     * 设置是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     *
     * @param switchRateLimit 是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     */
    public void setSwitchRateLimit(Integer switchRateLimit) {
        this.switchRateLimit = switchRateLimit;
    }

    /**
     * 获取限流速度 speed/s
     *
     * @return rateLimit - 限流速度 speed/s
     */
    public Integer getRateLimit() {
        return rateLimit;
    }

    /**
     * 设置限流速度 speed/s
     *
     * @param rateLimit 限流速度 speed/s
     */
    public void setRateLimit(Integer rateLimit) {
        this.rateLimit = rateLimit;
    }

    /**
     * 获取创建时间
     *
     * @return createTime - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return handleTime - 更新时间
     */
    public Date getHandleTime() {
        return handleTime;
    }

    /**
     * 设置更新时间
     *
     * @param handleTime 更新时间
     */
    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    /**
     * 获取更新人
     *
     * @return handler - 更新人
     */
    public String getHandler() {
        return handler;
    }

    /**
     * 设置更新人
     *
     * @param handler 更新人
     */
    public void setHandler(String handler) {
        this.handler = handler;
    }

    /**
     * 获取负责人
     *
     * @return coder - 负责人
     */
    public String getCoder() {
        return coder;
    }

    /**
     * 设置负责人
     *
     * @param coder 负责人
     */
    public void setCoder(String coder) {
        this.coder = coder;
    }

    /**
     * 获取是否开启log存储
     *
     * @return switchLog - 是否开启log存储
     */
    public Integer getSwitchLog() {
        return switchLog;
    }

    /**
     * 设置是否开启log存储
     *
     * @param switchLog 是否开启log存储
     */
    public void setSwitchLog(Integer switchLog) {
        this.switchLog = switchLog;
    }

    /**
     * 获取请求头列表
     *
     * @return targetHeaders - 请求头列表
     */
    public String getTargetHeaders() {
        return targetHeaders;
    }

    /**
     * 设置请求头列表
     *
     * @param targetHeaders 请求头列表
     */
    public void setTargetHeaders(String targetHeaders) {
        this.targetHeaders = targetHeaders;
    }

    /**
     * 获取请求参数列表
     *
     * @return targetForms - 请求参数列表
     */
    public String getTargetForms() {
        return targetForms;
    }

    /**
     * 设置请求参数列表
     *
     * @param targetForms 请求参数列表
     */
    public void setTargetForms(String targetForms) {
        this.targetForms = targetForms;
    }

    /**
     * 获取预留元数据
     *
     * @return metaData - 预留元数据
     */
    public String getMetaData() {
        return metaData;
    }

    /**
     * 设置预留元数据
     *
     * @param metaData 预留元数据
     */
    public void setMetaData(String metaData) {
        this.metaData = metaData;
    }
}