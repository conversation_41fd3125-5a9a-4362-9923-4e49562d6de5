package com.sinoair.ceos.gateway.service.sys;

import com.sinoair.ceos.gateway.domain.dto.sys.ResListParam;
import com.sinoair.ceos.gateway.domain.dto.sys.ResSaveParam;
import com.sinoair.ceos.gateway.domain.model.GwResource;

import java.util.List;

/**
 * @Author: daxiong
 */
public interface ResourceService {

    /**
     * 资源信息列表
     * @param resListParam
     * @return
     */
    List<GwResource> selectListByParam(ResListParam resListParam);

    /**
     * 保存信息
     * @param resSaveParam
     * @return
     */
    GwResource save(ResSaveParam resSaveParam);

    /**
     * 查询未分配的资源信息
     * @param appKey
     * @return
     */
    List<GwResource> selectListNotPermission(String appKey);

}
