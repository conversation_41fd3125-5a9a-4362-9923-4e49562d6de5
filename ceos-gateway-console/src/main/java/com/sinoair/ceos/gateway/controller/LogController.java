package com.sinoair.ceos.gateway.controller;

import com.sinoair.ceos.gateway.common.dto.api.ApiResult;
import com.sinoair.ceos.gateway.common.mongo.GatewayApiLogEntity;
import com.sinoair.ceos.gateway.common.util.ApiResultBuilder;
import com.sinoair.ceos.gateway.service.sys.LogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: daxiong
 */
@RestController
@RequestMapping("/log/")
public class LogController {

    @Autowired
    private LogService logService;

    /**
     * 轨迹查询
     * @param messageId
     * @param searchCode
     * @param searchType
     * @return
     */
    @CrossOrigin(origins = "*")
    @PostMapping("/gateway/query")
    public ResponseEntity gatewayLog(String messageId,String searchCode,String searchType){
        List<GatewayApiLogEntity> gatewayApiLogEntityList = this.logService.selectApiLog(messageId,searchCode,searchType);
        ApiResult apiResult = ApiResultBuilder.createBuilder().success(gatewayApiLogEntityList).newApiResult();
        return new ResponseEntity<>(apiResult, HttpStatus.OK);
    }

}
