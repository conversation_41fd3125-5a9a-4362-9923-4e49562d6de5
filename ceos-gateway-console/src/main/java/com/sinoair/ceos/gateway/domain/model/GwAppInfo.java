package com.sinoair.ceos.gateway.domain.model;

import javax.persistence.*;
import java.util.Date;

/**
 * 表名：gw_app_info
*/
@Table(name = "gw_app_info")
public class GwAppInfo {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 应用ID
     */
    @Column(name = "app_key")
    private String appKey;

    /**
     * 应用密钥
     */
    @Column(name = "app_secret")
    private String appSecret;

    /**
     * 应用名称
     */
    @Column(name = "app_name")
    private String appName;

    /**
     * 应用描述
     */
    @Column(name = "app_description")
    private String appDescription;

    /**
     * 状态
     */
    @Column(name = "app_status")
    private Integer appStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留元数据
     */
    @Column(name = "meta_data")
    private String metaData;

    /**
     * 更新时间
     */
    @Column(name = "handle_time")
    private Date handleTime;

    /**
     * 更新人
     */
    private String handler;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取应用ID
     *
     * @return appKey - 应用ID
     */
    public String getAppKey() {
        return appKey;
    }

    /**
     * 设置应用ID
     *
     * @param appKey 应用ID
     */
    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    /**
     * 获取应用密钥
     *
     * @return appSecret - 应用密钥
     */
    public String getAppSecret() {
        return appSecret;
    }

    /**
     * 设置应用密钥
     *
     * @param appSecret 应用密钥
     */
    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    /**
     * 获取应用名称
     *
     * @return appName - 应用名称
     */
    public String getAppName() {
        return appName;
    }

    /**
     * 设置应用名称
     *
     * @param appName 应用名称
     */
    public void setAppName(String appName) {
        this.appName = appName;
    }

    /**
     * 获取应用描述
     *
     * @return appDescription - 应用描述
     */
    public String getAppDescription() {
        return appDescription;
    }

    /**
     * 设置应用描述
     *
     * @param appDescription 应用描述
     */
    public void setAppDescription(String appDescription) {
        this.appDescription = appDescription;
    }

    /**
     * 获取状态
     *
     * @return appStatus - 状态
     */
    public Integer getAppStatus() {
        return appStatus;
    }

    /**
     * 设置状态
     *
     * @param appStatus 状态
     */
    public void setAppStatus(Integer appStatus) {
        this.appStatus = appStatus;
    }

    /**
     * 获取创建时间
     *
     * @return createTime - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取备注
     *
     * @return remark - 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取预留元数据
     *
     * @return metaData - 预留元数据
     */
    public String getMetaData() {
        return metaData;
    }

    /**
     * 设置预留元数据
     *
     * @param metaData 预留元数据
     */
    public void setMetaData(String metaData) {
        this.metaData = metaData;
    }

    /**
     * 获取更新时间
     *
     * @return handleTime - 更新时间
     */
    public Date getHandleTime() {
        return handleTime;
    }

    /**
     * 设置更新时间
     *
     * @param handleTime 更新时间
     */
    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    /**
     * 获取更新人
     *
     * @return handler - 更新人
     */
    public String getHandler() {
        return handler;
    }

    /**
     * 设置更新人
     *
     * @param handler 更新人
     */
    public void setHandler(String handler) {
        this.handler = handler;
    }
}
