package com.sinoair.ceos.gateway.domain.model;

import javax.persistence.*;
import java.util.Date;

/**
 * 表名：gw_proxy_version
*/
@Table(name = "gw_proxy_version")
public class GwProxyVersion {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 服务版本
     */
    @Column(name = "proxy_version")
    private String proxyVersion;

    /**
     * 是否有效
     */
    @Column(name = "proxy_status")
    private Integer proxyStatus;

    /**
     * 更新时间
     */
    @Column(name = "handle_time")
    private Date handleTime;

    /**
     * 更新人
     */
    private String handler;

    /**
     * 备注
     */
    private String remark;

    /**
     * 内容
     */
    @Column(name = "proxy_info")
    private String proxyInfo;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取服务版本
     *
     * @return proxyVersion - 服务版本
     */
    public String getProxyVersion() {
        return proxyVersion;
    }

    /**
     * 设置服务版本
     *
     * @param proxyVersion 服务版本
     */
    public void setProxyVersion(String proxyVersion) {
        this.proxyVersion = proxyVersion;
    }

    /**
     * 获取是否有效
     *
     * @return proxyStatus - 是否有效
     */
    public Integer getProxyStatus() {
        return proxyStatus;
    }

    /**
     * 设置是否有效
     *
     * @param proxyStatus 是否有效
     */
    public void setProxyStatus(Integer proxyStatus) {
        this.proxyStatus = proxyStatus;
    }

    /**
     * 获取更新时间
     *
     * @return handleTime - 更新时间
     */
    public Date getHandleTime() {
        return handleTime;
    }

    /**
     * 设置更新时间
     *
     * @param handleTime 更新时间
     */
    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    /**
     * 获取更新人
     *
     * @return handler - 更新人
     */
    public String getHandler() {
        return handler;
    }

    /**
     * 设置更新人
     *
     * @param handler 更新人
     */
    public void setHandler(String handler) {
        this.handler = handler;
    }

    /**
     * 获取备注
     *
     * @return remark - 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取内容
     *
     * @return proxyInfo - 内容
     */
    public String getProxyInfo() {
        return proxyInfo;
    }

    /**
     * 设置内容
     *
     * @param proxyInfo 内容
     */
    public void setProxyInfo(String proxyInfo) {
        this.proxyInfo = proxyInfo;
    }
}
