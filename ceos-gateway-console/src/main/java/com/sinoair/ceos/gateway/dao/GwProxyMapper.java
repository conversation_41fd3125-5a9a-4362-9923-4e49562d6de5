package com.sinoair.ceos.gateway.dao;

import com.sinoair.ceos.gateway.common.dto.proxy.HttpProxyInfo;
import com.sinoair.ceos.gateway.core.config.MyMapper;
import com.sinoair.ceos.gateway.domain.dto.sys.ProxyListParam;
import com.sinoair.ceos.gateway.domain.model.GwProxy;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GwProxyMapper extends MyMapper<GwProxy> {

    /**
     * 查询在线的反向代理配置
     * @return
     */
    List<HttpProxyInfo> selectOnlineProxy();

    /**
     * 查询代理列表信息
     * @param param
     * @return
     */
    List<GwProxy> selectProxyList(@Param("param") ProxyListParam param);

    /**
     * 根据code查询
     * @param proxyCode
     * @return
     */
    GwProxy selectByCode(@Param("proxyCode") String proxyCode);
}
