package com.sinoair.ceos.gateway.domain.model;

import org.jetbrains.annotations.NotNull;

import javax.persistence.*;
import java.util.Date;

/**
 * 表名：gw_resource
*/
@Table(name = "gw_resource")
public class GwResource {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 资源code
     */
    @Column(name = "res_code")
    private String resCode;

    /**
     * 资源名称
     */
    @Column(name = "res_name")
    private String resName;

    /**
     * 对接应用：ceos、gtms、billing
     */
    @Column(name = "res_app")
    private String resApp;

    /**
     * 资源接口地址
     */
    @Column(name = "res_url")
    private String resUrl;

    /**
     * 资源地址请求方式：POST
     */
    @Column(name = "res_method")
    private String resMethod;

    /**
     * 状态： 0 禁用、1 正常、2 升级中、3 审核中
     */
    @Column(name = "res_status")
    private Integer resStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 处理人
     */
    private String handler;

    /**
     * 超时时间，毫秒，默认3000毫秒
     */
    private Long timeout;

    /**
     * 接口文档地址
     */
    @Column(name = "api_doc_url")
    private String apiDocUrl;

    /**
     * 默认header {}
     */
    @Column(name = "res_header")
    private String resHeader;

    /**
     * 默认cookie {}
     */
    @Column(name = "res_cookie")
    private String resCookie;

    /**
     * 默认form内容 {}
     */
    @Column(name = "res_form_data")
    private String resFormData;

    /**
     * 预留元数据
     */
    @Column(name = "meta_data")
    private String metaData;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取资源code
     *
     * @return resCode - 资源code
     */
    @NotNull
    public String getResCode() {
        return resCode;
    }

    /**
     * 设置资源code
     *
     * @param resCode 资源code
     */
    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    /**
     * 获取资源名称
     *
     * @return resName - 资源名称
     */
    public String getResName() {
        return resName;
    }

    /**
     * 设置资源名称
     *
     * @param resName 资源名称
     */
    public void setResName(String resName) {
        this.resName = resName;
    }

    /**
     * 获取对接应用：ceos、gtms、billing
     *
     * @return resApp - 对接应用：ceos、gtms、billing
     */
    public String getResApp() {
        return resApp;
    }

    /**
     * 设置对接应用：ceos、gtms、billing
     *
     * @param resApp 对接应用：ceos、gtms、billing
     */
    public void setResApp(String resApp) {
        this.resApp = resApp;
    }

    /**
     * 获取资源接口地址
     *
     * @return resUrl - 资源接口地址
     */
    public String getResUrl() {
        return resUrl;
    }

    /**
     * 设置资源接口地址
     *
     * @param resUrl 资源接口地址
     */
    public void setResUrl(String resUrl) {
        this.resUrl = resUrl;
    }

    /**
     * 获取资源地址请求方式：POST
     *
     * @return resMethod - 资源地址请求方式：POST
     */
    public String getResMethod() {
        return resMethod;
    }

    /**
     * 设置资源地址请求方式：POST
     *
     * @param resMethod 资源地址请求方式：POST
     */
    public void setResMethod(String resMethod) {
        this.resMethod = resMethod;
    }

    /**
     * 获取状态： 0 禁用、1 正常、2 升级中、3 审核中
     *
     * @return resStatus - 状态： 0 禁用、1 正常、2 升级中、3 审核中
     */
    public Integer getResStatus() {
        return resStatus;
    }

    /**
     * 设置状态： 0 禁用、1 正常、2 升级中、3 审核中
     *
     * @param resStatus 状态： 0 禁用、1 正常、2 升级中、3 审核中
     */
    public void setResStatus(Integer resStatus) {
        this.resStatus = resStatus;
    }

    /**
     * 获取创建时间
     *
     * @return createTime - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取备注
     *
     * @return remark - 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取处理人
     *
     * @return handler - 处理人
     */
    public String getHandler() {
        return handler;
    }

    /**
     * 设置处理人
     *
     * @param handler 处理人
     */
    public void setHandler(String handler) {
        this.handler = handler;
    }

    /**
     * 获取超时时间，毫秒，默认3000毫秒
     *
     * @return timeout - 超时时间，毫秒，默认3000毫秒
     */
    public Long getTimeout() {
        return timeout;
    }

    /**
     * 设置超时时间，毫秒，默认3000毫秒
     *
     * @param timeout 超时时间，毫秒，默认3000毫秒
     */
    public void setTimeout(Long timeout) {
        this.timeout = timeout;
    }

    /**
     * 获取接口文档地址
     *
     * @return apiDocUrl - 接口文档地址
     */
    public String getApiDocUrl() {
        return apiDocUrl;
    }

    /**
     * 设置接口文档地址
     *
     * @param apiDocUrl 接口文档地址
     */
    public void setApiDocUrl(String apiDocUrl) {
        this.apiDocUrl = apiDocUrl;
    }

    /**
     * 获取默认header {}
     *
     * @return resHeader - 默认header {}
     */
    public String getResHeader() {
        return resHeader;
    }

    /**
     * 设置默认header {}
     *
     * @param resHeader 默认header {}
     */
    public void setResHeader(String resHeader) {
        this.resHeader = resHeader;
    }

    /**
     * 获取默认cookie {}
     *
     * @return resCookie - 默认cookie {}
     */
    public String getResCookie() {
        return resCookie;
    }

    /**
     * 设置默认cookie {}
     *
     * @param resCookie 默认cookie {}
     */
    public void setResCookie(String resCookie) {
        this.resCookie = resCookie;
    }

    /**
     * 获取默认form内容 {}
     *
     * @return resFormData - 默认form内容 {}
     */
    public String getResFormData() {
        return resFormData;
    }

    /**
     * 设置默认form内容 {}
     *
     * @param resFormData 默认form内容 {}
     */
    public void setResFormData(String resFormData) {
        this.resFormData = resFormData;
    }

    /**
     * 获取预留元数据
     *
     * @return metaData - 预留元数据
     */
    public String getMetaData() {
        return metaData;
    }

    /**
     * 设置预留元数据
     *
     * @param metaData 预留元数据
     */
    public void setMetaData(String metaData) {
        this.metaData = metaData;
    }
}
