package com.sinoair.ceos.gateway.service.gateway.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.sinoair.ceos.gateway.common.dto.app.AppInfo;
import com.sinoair.ceos.gateway.common.dto.app.AppResource;
import com.sinoair.ceos.gateway.common.dto.app.GatewaySyncInfoDto;
import com.sinoair.ceos.gateway.dao.GwAppInfoMapper;
import com.sinoair.ceos.gateway.dao.GwAppPermissionMapper;
import com.sinoair.ceos.gateway.dao.GwResourceMapper;
import com.sinoair.ceos.gateway.dao.GwVersionMapper;
import com.sinoair.ceos.gateway.domain.model.GwAppInfo;
import com.sinoair.ceos.gateway.domain.model.GwAppPermission;
import com.sinoair.ceos.gateway.domain.model.GwResource;
import com.sinoair.ceos.gateway.service.gateway.GatewayInfoService;
import com.sinoair.ceos.gateway.service.gateway.GatewayProxyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: daxiong
 */
@Service
public class GatewayInfoServiceImpl implements GatewayInfoService {

    @Autowired
    private GwAppInfoMapper gwAppInfoMapper;

    @Autowired
    private GwAppPermissionMapper gwAppPermissionMapper;

    @Autowired
    private GwResourceMapper gwResourceMapper;

    @Autowired
    private GwVersionMapper gwVersionMapper;

    @Autowired
    private GatewayProxyService gatewayProxyService;

    /**
     * 查询应用信息
     *
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public GatewaySyncInfoDto selectAppInfo(String linkInfo) {
        List<GwAppInfo> gwAppInfos = gwAppInfoMapper.selectAll();
        List<GwAppPermission> gwAppPermissions = gwAppPermissionMapper.selectAll();
        List<GwResource> gwResources = gwResourceMapper.selectAll();
        ImmutableMap<String, GwResource> stringGwResourceImmutableMap = Maps.uniqueIndex(gwResources, GwResource::getResCode);

        List<AppInfo> appInfos = new ArrayList<>();
        for (GwAppInfo gwAppInfo : gwAppInfos){
            if (gwAppInfo.getAppStatus() == 1){
                AppInfo appInfo = new AppInfo();
                appInfo.setAppKey(gwAppInfo.getAppKey());
                appInfo.setAppDescription(gwAppInfo.getAppDescription());
                appInfo.setAppName(gwAppInfo.getAppName());
                appInfo.setAppSecret(gwAppInfo.getAppSecret());
                appInfo.setAppStatus(gwAppInfo.getAppStatus());
                appInfo.setMetaData(gwAppInfo.getMetaData());
                appInfo.setRemark(gwAppInfo.getRemark());

                List<AppResource> appResources = new ArrayList<>();
                for (GwAppPermission gwAppPermission : gwAppPermissions) {
                    if (gwAppPermission.getSwitchStatus() == 1
                            && gwAppInfo.getAppKey().equals(gwAppPermission.getAppKey())){
                        GwResource resource = stringGwResourceImmutableMap.get(gwAppPermission.getResCode());
                        if (resource != null && resource.getResStatus() == 1){
                            AppResource appResource = new AppResource();
                            appResource.setResCode(resource.getResCode());
                            appResource.setResName(resource.getResName());
                            appResource.setResApp(resource.getResApp());
                            appResource.setResUrl(resource.getResUrl());
                            appResource.setResMethod(resource.getResMethod());
                            appResource.setResStatus(resource.getResStatus());
                            appResource.setTimeout(resource.getTimeout().intValue());

                            if ( resource.getResHeader() != null){
                                Map<String, String> header = JSON.parseObject(resource.getResHeader(), new TypeReference<Map<String, String>>() {});
                                appResource.setResHeader(header);
                            }
                            if (resource.getResCookie() != null){
                                Map<String, String> cookie = JSON.parseObject(resource.getResCookie(), new TypeReference<Map<String, String>>() {});
                                appResource.setResCookie(cookie);
                            }
                            if (resource.getResCookie() != null){
                                Map<String, String> formData = JSON.parseObject(resource.getResFormData(), new TypeReference<Map<String, String>>() {});
                                appResource.setResFormData(formData);
                            }
                            appResource.setSwitchStatus(gwAppPermission.getSwitchStatus());
                            appResource.setSwitchSignature(gwAppPermission.getSwitchSignature());
                            appResource.setSwitchTimestamp(gwAppPermission.getSwitchTimestamp());
                            appResource.setSwitchRequestBody(gwAppPermission.getSwitchRequestBody());
                            appResource.setSwitchRateLimit(gwAppPermission.getSwitchRateLimit());
                            appResource.setRateLimit(gwAppPermission.getRateLimit());
                            appResource.setSwitchLog(gwAppPermission.getSwitchLog());
                            appResource.setSwitchIdempotent(gwAppPermission.getSwitchIdempotent());
                            appResource.setIdempotentTimeOut(gwAppPermission.getIdempotentTimeOut());
                            appResources.add(appResource);
                        }
                    }
                }
                appInfo.setAppResources(appResources);
                appInfos.add(appInfo);
            }
        }

        GatewaySyncInfoDto gatewaySyncInfoDto = new GatewaySyncInfoDto();
        gatewaySyncInfoDto.setAppInfos(appInfos);
        gatewaySyncInfoDto.setGatewayVersion(this.gwVersionMapper.selectLatest().getGwVersion());
        gatewaySyncInfoDto.setGatewayProxySyncInfo(gatewayProxyService.selectProxyInfo(linkInfo));
        return gatewaySyncInfoDto;
    }
}
