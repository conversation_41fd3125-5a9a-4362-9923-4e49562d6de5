package com.sinoair.ceos.gateway.dao;

import com.sinoair.ceos.gateway.core.config.MyMapper;
import com.sinoair.ceos.gateway.domain.dto.sys.AppPermission;
import com.sinoair.ceos.gateway.domain.dto.sys.AppPermissionListParam;
import com.sinoair.ceos.gateway.domain.model.GwAppPermission;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GwAppPermissionMapper extends MyMapper<GwAppPermission> {

    /**
     * 查询列表信息
     * @param param
     * @return
     */
    List<AppPermission> selectTableList(@Param("param") AppPermissionListParam param);

}
