package com.sinoair.ceos.gateway.service.gateway;

import com.sinoair.ceos.gateway.common.dto.proxy.GatewayProxySyncInfo;
import com.sinoair.ceos.gateway.domain.dto.sys.ProxyListParam;
import com.sinoair.ceos.gateway.domain.dto.sys.ProxySaveParam;
import com.sinoair.ceos.gateway.domain.model.GwProxy;

import java.util.List;

/**
 * @author: 大雄
 */
public interface GatewayProxyService {

    /**
     * 查询代理信息
     * @return
     */
    GatewayProxySyncInfo selectProxyInfo(String linkInfo);

    /**
     * 查询代理列表信息
     * @param param
     * @return
     */
    List<GwProxy> selectProxyList(ProxyListParam param);

    /**
     * 保存代理信息
     * @param param
     */
    GwProxy saveProxy(ProxySaveParam param);

    /**
     * 更新版本
     */
    void updateVersion(String handler,String proxyInfo,String remark);

}
