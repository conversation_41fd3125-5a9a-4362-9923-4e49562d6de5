package com.sinoair.ceos.gateway.domain.model;

import javax.persistence.*;
import java.util.Date;

/**
 * 表名：gw_version
*/
@Table(name = "gw_version")
public class GwVersion {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 服务版本
     */
    @Column(name = "gw_version")
    private String gwVersion;

    /**
     * 是否有效
     */
    @Column(name = "gw_status")
    private Integer gwStatus;

    /**
     * 更新时间
     */
    @Column(name = "handle_time")
    private Date handleTime;

    /**
     * 更新人
     */
    private String handler;

    /**
     * 备注
     */
    private String remark;

    /**
     * 内容
     */
    @Column(name = "gw_info")
    private String gwInfo;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取服务版本
     *
     * @return gwVersion - 服务版本
     */
    public String getGwVersion() {
        return gwVersion;
    }

    /**
     * 设置服务版本
     *
     * @param gwVersion 服务版本
     */
    public void setGwVersion(String gwVersion) {
        this.gwVersion = gwVersion;
    }

    /**
     * 获取是否有效
     *
     * @return gwStatus - 是否有效
     */
    public Integer getGwStatus() {
        return gwStatus;
    }

    /**
     * 设置是否有效
     *
     * @param gwStatus 是否有效
     */
    public void setGwStatus(Integer gwStatus) {
        this.gwStatus = gwStatus;
    }

    /**
     * 获取更新时间
     *
     * @return handleTime - 更新时间
     */
    public Date getHandleTime() {
        return handleTime;
    }

    /**
     * 设置更新时间
     *
     * @param handleTime 更新时间
     */
    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    /**
     * 获取更新人
     *
     * @return handler - 更新人
     */
    public String getHandler() {
        return handler;
    }

    /**
     * 设置更新人
     *
     * @param handler 更新人
     */
    public void setHandler(String handler) {
        this.handler = handler;
    }

    /**
     * 获取备注
     *
     * @return remark - 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取内容
     *
     * @return gwInfo - 内容
     */
    public String getGwInfo() {
        return gwInfo;
    }

    /**
     * 设置内容
     *
     * @param gwInfo 内容
     */
    public void setGwInfo(String gwInfo) {
        this.gwInfo = gwInfo;
    }
}
