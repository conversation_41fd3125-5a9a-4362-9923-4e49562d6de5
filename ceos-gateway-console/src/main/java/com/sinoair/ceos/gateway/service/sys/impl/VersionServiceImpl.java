package com.sinoair.ceos.gateway.service.sys.impl;

import com.alibaba.fastjson2.JSONObject;
import com.sinoair.ceos.gateway.common.dto.app.GatewaySyncInfoDto;
import com.sinoair.ceos.gateway.common.util.DateUtil;
import com.sinoair.ceos.gateway.dao.GwVersionMapper;
import com.sinoair.ceos.gateway.domain.model.GwVersion;
import com.sinoair.ceos.gateway.service.gateway.GatewayInfoService;
import com.sinoair.ceos.gateway.service.sys.VersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @author: 大雄
 */
@Service
public class VersionServiceImpl implements VersionService {

    @Autowired
    private GatewayInfoService gatewayInfoService;

    @Autowired
    private GwVersionMapper gwVersionMapper;

    /**
     * 更新版本
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateVersion(String handler,String remark) {
        // 获取最高的版本
        GwVersion gwVersion = gwVersionMapper.selectLatest();
        Date now = new Date();
        // 更新版本
        String s = DateUtil.parseDateStr(DateUtil.DATE_yyyyMMdd,new Date());
        String[] aa = gwVersion.getGwVersion().split("-");
        String newVersion;
        if (aa.length != 2){
            newVersion = s + "-1";
        }else{
            if (s.equals(aa[0])){
                newVersion = s + "-" + (Integer.valueOf(aa[1]) + 1);
            }else{
                newVersion = s + "-1";
            }
        }
        GatewaySyncInfoDto gatewaySyncInfoDto = gatewayInfoService.selectAppInfo(null);
        gatewaySyncInfoDto.setGatewayVersion(newVersion);
        GwVersion version = new GwVersion();
        version.setGwVersion(newVersion);
        version.setGwInfo(JSONObject.toJSONString(gatewaySyncInfoDto));
        version.setGwStatus(1);
        version.setHandleTime(now);
        version.setHandler(handler);
        version.setRemark(remark);
        this.gwVersionMapper.insert(version);
        gwVersion.setGwStatus(0);
        gwVersion.setHandleTime(now);
        this.gwVersionMapper.updateByPrimaryKey(gwVersion);
    }

    /**
     * 查询最新的版本
     *
     * @return
     */
    @Override
    public GwVersion selectLatest() {
        return this.gwVersionMapper.selectLatest();
    }
}
