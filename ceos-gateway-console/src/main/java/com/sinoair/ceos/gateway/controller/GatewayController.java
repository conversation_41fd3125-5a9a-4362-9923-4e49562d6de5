package com.sinoair.ceos.gateway.controller;

import com.sinoair.ceos.gateway.common.dto.api.ApiResult;
import com.sinoair.ceos.gateway.common.dto.app.GatewaySyncInfoDto;
import com.sinoair.ceos.gateway.common.util.ApiResultBuilder;
import com.sinoair.ceos.gateway.common.util.HttpUtil;
import com.sinoair.ceos.gateway.common.util.SignatureUtil;
import com.sinoair.ceos.gateway.service.gateway.GatewayInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.util.StringUtil;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: daxiong
 */
@RestController
@RequestMapping("gateway")
public class GatewayController {

    @Value("${ceos-gateway.secret.link.app-key}")
    private String linkAppKey;
    @Value("${ceos-gateway.secret.link.app-secret}")
    private String linkAppSecret;

    private static final String RESOURCES_CODE_GATEWAY_INFO = "SYS-CEOS-GATEWAY-INFO";

    @Autowired
    private GatewayInfoService gatewayInfoService;

    /**
     * 获取app信息
     * 获取应用信息
     * @return
     */
    @PostMapping("info")
    public HttpEntity gatewayInfo(HttpServletRequest request){
        // 请求方时间戳 毫秒时间戳
        String timestamp = request.getHeader("Timestamp");
        // 资源code
        String resourceCode = request.getHeader("Resource-Code");
        // appKey 接入id
        String appKey = request.getHeader("App-Key");
        // 签名
        String signature = request.getHeader("Signature");

        if (!RESOURCES_CODE_GATEWAY_INFO.equals(resourceCode)){
            ApiResult apiResult = ApiResultBuilder.createBuilder()
                    .code("-1")
                    .message("资源代码无效")
                    .newApiResult();
            return new ResponseEntity<>(apiResult, HttpStatus.OK);
        }
        if (StringUtil.isEmpty(timestamp)){
            ApiResult apiResult = ApiResultBuilder.createBuilder()
                    .code("-1")
                    .message("时间戳无效")
                    .newApiResult();
            return new ResponseEntity<>(apiResult, HttpStatus.OK);
        }
        if (!linkAppKey.equals(appKey)){
            ApiResult apiResult = ApiResultBuilder.createBuilder()
                    .code("-1")
                    .message("appKey无效")
                    .newApiResult();
            return new ResponseEntity<>(apiResult, HttpStatus.OK);
        }
        String linkInfo = HttpUtil.readRequest(request);
        boolean b = SignatureUtil.checkSign(signature, appKey, linkAppSecret, resourceCode, linkInfo, Long.valueOf(timestamp));
        if (!b){
            ApiResult apiResult = ApiResultBuilder.createBuilder()
                    .code("-1")
                    .message("签名无效")
                    .newApiResult();
            return new ResponseEntity<>(apiResult, HttpStatus.OK);
        }else{
            GatewaySyncInfoDto gatewaySyncInfoDto = this.gatewayInfoService.selectAppInfo(linkInfo);
            // todo 存储日志 应用 获取记录，心跳信息，作为客户端健康检测的标识
            ApiResult apiResult = ApiResultBuilder.createBuilder()
                    .success(gatewaySyncInfoDto)
                    .newApiResult();
            return new ResponseEntity<>(apiResult,HttpStatus.OK);
        }
    }
}
