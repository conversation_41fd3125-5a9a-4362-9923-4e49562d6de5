package com.sinoair.ceos.gateway.controller;

import com.sinoair.ceos.gateway.service.sys.VersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 大雄
 */
@RestController
@RequestMapping("/sys/monitor")
public class MonitorController {

    @Autowired
    private VersionService versionService;

    /**
     * 预警监控
     * @param request
     * @return
     */
    @GetMapping("/hb")
    public String hb(HttpServletRequest request){
        return versionService.selectLatest().getGwVersion();
    }

}
