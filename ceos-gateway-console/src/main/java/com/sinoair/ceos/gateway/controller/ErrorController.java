package com.sinoair.ceos.gateway.controller;

import com.sinoair.ceos.gateway.common.dto.api.ApiResult;
import com.sinoair.ceos.gateway.common.util.ApiResultBuilder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

/**
 * @Author: daxiong
 */
@RestController
@RequestMapping("/error")
public class ErrorController {

    @RequestMapping("/400")
    public ResponseEntity error400(){
        ApiResult apiResult = ApiResultBuilder.createBuilder()
                .code("E"+HttpStatus.BAD_REQUEST.value())
                .message(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .messageId(UUID.randomUUID().toString())
                .newApiResult();
        return  new ResponseEntity<>(api<PERSON><PERSON>ult, HttpStatus.BAD_REQUEST);
    }

    @RequestMapping("/401")
    public ResponseEntity error401(){
        ApiResult apiResult = ApiResultBuilder.createBuilder()
                .code("E"+HttpStatus.UNAUTHORIZED.value())
                .message(HttpStatus.UNAUTHORIZED.getReasonPhrase())
                .messageId(UUID.randomUUID().toString())
                .newApiResult();
        return  new ResponseEntity<>(apiResult, HttpStatus.UNAUTHORIZED);
    }

    @RequestMapping("/404")
    public ResponseEntity error404(){
        ApiResult apiResult = ApiResultBuilder.createBuilder()
                .code("E"+HttpStatus.NOT_FOUND.value())
                .message(HttpStatus.NOT_FOUND.getReasonPhrase())
                .messageId(UUID.randomUUID().toString())
                .newApiResult();
        return  new ResponseEntity<>(apiResult, HttpStatus.NOT_FOUND);
    }

    @RequestMapping("/403")
    public ResponseEntity error403(){
        ApiResult apiResult = ApiResultBuilder.createBuilder()
                .code("E"+HttpStatus.FORBIDDEN.value())
                .message(HttpStatus.FORBIDDEN.getReasonPhrase())
                .messageId(UUID.randomUUID().toString())
                .newApiResult();
        return  new ResponseEntity<>(apiResult, HttpStatus.FORBIDDEN);
    }

    @RequestMapping("/405")
    public ResponseEntity error405(){
        ApiResult apiResult = ApiResultBuilder.createBuilder()
                .code("E"+HttpStatus.METHOD_NOT_ALLOWED.value())
                .message(HttpStatus.METHOD_NOT_ALLOWED.getReasonPhrase())
                .messageId(UUID.randomUUID().toString())
                .newApiResult();
        return  new ResponseEntity<>(apiResult, HttpStatus.METHOD_NOT_ALLOWED);
    }

    @RequestMapping("/500")
    public ResponseEntity error500(){
        ApiResult apiResult = ApiResultBuilder.createBuilder()
                .code("E"+HttpStatus.INTERNAL_SERVER_ERROR.value())
                .message(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase())
                .messageId(UUID.randomUUID().toString())
                .newApiResult();
        return  new ResponseEntity<>(apiResult, HttpStatus.INTERNAL_SERVER_ERROR);
    }

}
