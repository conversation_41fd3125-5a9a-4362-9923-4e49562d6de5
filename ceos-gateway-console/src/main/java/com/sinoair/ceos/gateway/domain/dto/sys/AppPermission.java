package com.sinoair.ceos.gateway.domain.dto.sys;

import com.sinoair.ceos.gateway.domain.model.GwAppPermission;

/**
 * @author: 大雄
 */
public class AppPermission extends GwAppPermission {

    private String resName;

    private String resApp;

    private Integer resStatus;

    private Long timeout;

    private String apiDocUrl;

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getResApp() {
        return resApp;
    }

    public void setResApp(String resApp) {
        this.resApp = resApp;
    }

    public Integer getResStatus() {
        return resStatus;
    }

    public void setResStatus(Integer resStatus) {
        this.resStatus = resStatus;
    }

    public Long getTimeout() {
        return timeout;
    }

    public void setTimeout(Long timeout) {
        this.timeout = timeout;
    }

    public String getApiDocUrl() {
        return apiDocUrl;
    }

    public void setApiDocUrl(String apiDocUrl) {
        this.apiDocUrl = apiDocUrl;
    }
}
