package com.sinoair.ceos.gateway.domain.model;

import javax.persistence.*;
import java.util.Date;

/**
 * 表名：gw_dict
*/
@Table(name = "gw_dict")
public class GwDict {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 字典组CODE
     */
    @Column(name = "group_code")
    private String groupCode;

    /**
     * 字典组概述
     */
    @Column(name = "group_describe")
    private String groupDescribe;

    /**
     * 状态
     */
    @Column(name = "dict_status")
    private Integer dictStatus;

    /**
     * 创建时间
     */
    @Column(name = "handle_time")
    private Date handleTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 字典数据源
     */
    @Column(name = "dict_data")
    private String dictData;

    /**
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取字典组CODE
     *
     * @return groupCode - 字典组CODE
     */
    public String getGroupCode() {
        return groupCode;
    }

    /**
     * 设置字典组CODE
     *
     * @param groupCode 字典组CODE
     */
    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    /**
     * 获取字典组概述
     *
     * @return groupDescribe - 字典组概述
     */
    public String getGroupDescribe() {
        return groupDescribe;
    }

    /**
     * 设置字典组概述
     *
     * @param groupDescribe 字典组概述
     */
    public void setGroupDescribe(String groupDescribe) {
        this.groupDescribe = groupDescribe;
    }

    /**
     * 获取状态
     *
     * @return dictStatus - 状态
     */
    public Integer getDictStatus() {
        return dictStatus;
    }

    /**
     * 设置状态
     *
     * @param dictStatus 状态
     */
    public void setDictStatus(Integer dictStatus) {
        this.dictStatus = dictStatus;
    }

    /**
     * 获取创建时间
     *
     * @return handleTime - 创建时间
     */
    public Date getHandleTime() {
        return handleTime;
    }

    /**
     * 设置创建时间
     *
     * @param handleTime 创建时间
     */
    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    /**
     * 获取备注
     *
     * @return remark - 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取字典数据源
     *
     * @return dictData - 字典数据源
     */
    public String getDictData() {
        return dictData;
    }

    /**
     * 设置字典数据源
     *
     * @param dictData 字典数据源
     */
    public void setDictData(String dictData) {
        this.dictData = dictData;
    }
}
