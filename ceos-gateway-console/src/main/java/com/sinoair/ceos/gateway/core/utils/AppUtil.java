package com.sinoair.ceos.gateway.core.utils;

import org.apache.commons.codec.digest.DigestUtils;
import java.util.Base64;
import java.util.UUID;

/**
 * @Author: daxiong
 */
public class AppUtil {

    private static final String CEOS_GATEWAY = "ceos-gateway";

    private static final String IDX = "|";

    /**
     * 创建随机信息，appKey和appSecret
     * @param bean
     * @return
     */
    public static String createRandomInfo(String bean){
        String appKeyBuilder = bean +
                IDX +
                UUID.randomUUID() +
                IDX +
                System.currentTimeMillis() +
                IDX +
                CEOS_GATEWAY;
        String appKey = DigestUtils.md5Hex(appKeyBuilder);
        // byte[] bytes = Base64.encodeBase64(appKey.getBytes());
        return Base64.getEncoder().encodeToString(appKey.getBytes());
    }

    public static void main(String[] args) {
        String sss = createRandomInfo("********************************************");
        System.out.println(sss);
    }

}
