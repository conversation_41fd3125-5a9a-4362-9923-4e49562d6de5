package com.sinoair.ceos.gateway.service.sys.impl;

import com.sinoair.ceos.gateway.common.exception.CeosGatewayException;
import com.sinoair.ceos.gateway.common.util.StringUtil;
import com.sinoair.ceos.gateway.dao.GwResourceMapper;
import com.sinoair.ceos.gateway.domain.dto.sys.ResListParam;
import com.sinoair.ceos.gateway.domain.dto.sys.ResSaveParam;
import com.sinoair.ceos.gateway.domain.model.GwResource;
import com.sinoair.ceos.gateway.service.sys.ResourceService;
import com.sinoair.ceos.gateway.service.sys.VersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Author: daxiong
 */
@Service
public class ResourceServiceImpl implements ResourceService {

    @Autowired
    private GwResourceMapper gwResourceMapper;

    @Autowired
    private VersionService versionService;

    /**
     * 资源信息列表
     *
     * @param resListParam
     * @return
     */
    @Override
    public List<GwResource> selectListByParam(ResListParam resListParam) {
        return this.gwResourceMapper.selectTableList(resListParam);
    }

    /**
     * 保存信息
     *
     * @param resSaveParam
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public GwResource save(ResSaveParam resSaveParam) {
        if (StringUtil.isEmpty(resSaveParam.getResName())) {
            throw new CeosGatewayException("资源名称不能为空", "-1");
        }
        if (StringUtil.isEmpty(resSaveParam.getResApp())) {
            throw new CeosGatewayException("对接应用不能为空", "-1");
        }
        if (StringUtil.isEmpty(resSaveParam.getResUrl())) {
            throw new CeosGatewayException("URL不能为空", "-1");
        }
        if (StringUtil.isEmpty(resSaveParam.getResMethod())) {
            throw new CeosGatewayException("请求方法不能为空", "-1");
        }
        GwResource gwResource = new GwResource();
        gwResource.setResName(resSaveParam.getResName());
        gwResource.setResApp(resSaveParam.getResApp());
        gwResource.setResUrl(resSaveParam.getResUrl());
        gwResource.setResMethod(resSaveParam.getResMethod());
        gwResource.setResStatus(resSaveParam.getResStatus());
        gwResource.setRemark(resSaveParam.getRemark());
        gwResource.setTimeout(resSaveParam.getTimeout());
        gwResource.setApiDocUrl(resSaveParam.getApiDocUrl());
        if (StringUtil.isEmpty((resSaveParam.getResHeader()))){
            gwResource.setResHeader("{}");
        }else{
            gwResource.setResHeader(resSaveParam.getResHeader());
        }
        if (StringUtil.isEmpty(resSaveParam.getResFormData())){
            gwResource.setResFormData("{}");
        }else{
            gwResource.setResFormData(resSaveParam.getResFormData());
        }
        if (StringUtil.isEmpty(resSaveParam.getResCookie())){
            gwResource.setResCookie("{}");
        }else{
            gwResource.setResCookie(resSaveParam.getResCookie());
        }
        if (StringUtil.isEmpty(resSaveParam.getMetaData())){
            gwResource.setMetaData("{}");
        }else{
            gwResource.setMetaData(resSaveParam.getMetaData());
        }
        gwResource.setHandler(resSaveParam.getHandler());

        if ("add".equals(resSaveParam.getSaveType())) {
            if (StringUtil.isEmpty(resSaveParam.getResCode())) {
                throw new CeosGatewayException("资源代码不能为空", "-1");
            }
            if ( this.gwResourceMapper.selectByPrimaryKey(resSaveParam.getId()) != null ) {
                throw new CeosGatewayException("资源代码已存在，请重新录入", "-1");
            }
            gwResource.setResCode(resSaveParam.getResCode());
            gwResource.setCreateTime(new Date());
            this.gwResourceMapper.insert(gwResource);
        }else{
            if (this.gwResourceMapper.selectByPrimaryKey(resSaveParam.getId()) == null){
                throw new CeosGatewayException("资源信息不存在", "-1");
            }
            gwResource.setId(resSaveParam.getId());
            this.gwResourceMapper.updateByPrimaryKeySelective(gwResource);
        }
        // 更新版本
        this.versionService.updateVersion(resSaveParam.getHandler(), "API资源更新");
        return gwResource;
    }

    /**
     * 查询未分配的资源信息
     *
     * @param appKey
     * @return
     */
    @Override
    public List<GwResource> selectListNotPermission(String appKey) {
        return this.gwResourceMapper.selectListNotPermission(appKey);
    }
}
