package com.sinoair.ceos.gateway.domain.model;

import javax.persistence.*;
import java.util.Date;

/**
 * 表名：gw_app_permission
 */
@Table(name = "gw_app_permission")
public class GwAppPermission {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * appid
     */
    @Column(name = "app_key")
    private String appKey;

    /**
     * 资源code
     */
    @Column(name = "res_code")
    private String resCode;

    /**
     * 是否有效 1 0
     */
    @Column(name = "switch_status")
    private Integer switchStatus;

    /**
     * 是否开启签名校验 1
     */
    @Column(name = "switch_signature")
    private Integer switchSignature;

    /**
     * 是否开启时间戳校验 1
     */
    @Column(name = "switch_timestamp")
    private Integer switchTimestamp;

    /**
     * 是否开启请求体校验 1 （json）
     */
    @Column(name = "switch_request_body")
    private Integer switchRequestBody;

    /**
     * 是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     */
    @Column(name = "switch_rate_limit")
    private Integer switchRateLimit;

    /**
     * 限流速度 speed/s
     */
    @Column(name = "rate_limit")
    private Integer rateLimit;

    /**
     * 是否开启log存储
     */
    @Column(name = "switch_log")
    private Integer switchLog;

    /**
     * log存储天数，默认20
     */
    @Column(name = "log_days")
    private Integer logDays;

    /**
     * 是否开启幂等
     */
    @Column(name = "switch_idempotent")
    private Integer switchIdempotent;

    /**
     * 幂等超时时间：单位秒，默认10秒
     */
    @Column(name = "idempotent_time_out")
    private Integer idempotentTimeOut;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 描述
     */
    private String remark;

    /**
     * 预留元数据
     */
    @Column(name = "meta_data")
    private String metaData;

    /**
     * 更新时间
     */
    @Column(name = "handle_time")
    private Date handleTime;

    /**
     * 更新人
     */
    private String handler;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取appid
     *
     * @return appKey - appid
     */
    public String getAppKey() {
        return appKey;
    }

    /**
     * 设置appid
     *
     * @param appKey appid
     */
    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    /**
     * 获取资源code
     *
     * @return resCode - 资源code
     */
    public String getResCode() {
        return resCode;
    }

    /**
     * 设置资源code
     *
     * @param resCode 资源code
     */
    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    /**
     * 获取是否有效 1 0
     *
     * @return switchStatus - 是否有效 1 0
     */
    public Integer getSwitchStatus() {
        return switchStatus;
    }

    /**
     * 设置是否有效 1 0
     *
     * @param switchStatus 是否有效 1 0
     */
    public void setSwitchStatus(Integer switchStatus) {
        this.switchStatus = switchStatus;
    }

    /**
     * 获取是否开启签名校验 1
     *
     * @return switchSignature - 是否开启签名校验 1
     */
    public Integer getSwitchSignature() {
        return switchSignature;
    }

    /**
     * 设置是否开启签名校验 1
     *
     * @param switchSignature 是否开启签名校验 1
     */
    public void setSwitchSignature(Integer switchSignature) {
        this.switchSignature = switchSignature;
    }

    /**
     * 获取是否开启时间戳校验 1
     *
     * @return switchTimestamp - 是否开启时间戳校验 1
     */
    public Integer getSwitchTimestamp() {
        return switchTimestamp;
    }

    /**
     * 设置是否开启时间戳校验 1
     *
     * @param switchTimestamp 是否开启时间戳校验 1
     */
    public void setSwitchTimestamp(Integer switchTimestamp) {
        this.switchTimestamp = switchTimestamp;
    }

    /**
     * 获取是否开启请求体校验 1 （json）
     *
     * @return switchRequestBody - 是否开启请求体校验 1 （json）
     */
    public Integer getSwitchRequestBody() {
        return switchRequestBody;
    }

    /**
     * 设置是否开启请求体校验 1 （json）
     *
     * @param switchRequestBody 是否开启请求体校验 1 （json）
     */
    public void setSwitchRequestBody(Integer switchRequestBody) {
        this.switchRequestBody = switchRequestBody;
    }

    /**
     * 获取是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     *
     * @return switchRateLimit - 是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     */
    public Integer getSwitchRateLimit() {
        return switchRateLimit;
    }

    /**
     * 设置是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     *
     * @param switchRateLimit 是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     */
    public void setSwitchRateLimit(Integer switchRateLimit) {
        this.switchRateLimit = switchRateLimit;
    }

    /**
     * 获取限流速度 speed/s
     *
     * @return rateLimit - 限流速度 speed/s
     */
    public Integer getRateLimit() {
        return rateLimit;
    }

    /**
     * 设置限流速度 speed/s
     *
     * @param rateLimit 限流速度 speed/s
     */
    public void setRateLimit(Integer rateLimit) {
        this.rateLimit = rateLimit;
    }

    /**
     * 获取是否开启log存储
     *
     * @return switchLog - 是否开启log存储
     */
    public Integer getSwitchLog() {
        return switchLog;
    }

    /**
     * 设置是否开启log存储
     *
     * @param switchLog 是否开启log存储
     */
    public void setSwitchLog(Integer switchLog) {
        this.switchLog = switchLog;
    }

    /**
     * 获取log存储天数，默认20
     *
     * @return logDays - log存储天数，默认20
     */
    public Integer getLogDays() {
        return logDays;
    }

    /**
     * 设置log存储天数，默认20
     *
     * @param logDays log存储天数，默认20
     */
    public void setLogDays(Integer logDays) {
        this.logDays = logDays;
    }

    /**
     * 获取是否开启幂等
     *
     * @return switchIdempotent - 是否开启幂等
     */
    public Integer getSwitchIdempotent() {
        return switchIdempotent;
    }

    /**
     * 设置是否开启幂等
     *
     * @param switchIdempotent 是否开启幂等
     */
    public void setSwitchIdempotent(Integer switchIdempotent) {
        this.switchIdempotent = switchIdempotent;
    }

    /**
     * 获取幂等超时时间：单位秒，默认10秒
     *
     * @return idempotentTimeOut - 幂等超时时间：单位秒，默认10秒
     */
    public Integer getIdempotentTimeOut() {
        return idempotentTimeOut;
    }

    /**
     * 设置幂等超时时间：单位秒，默认10秒
     *
     * @param idempotentTimeOut 幂等超时时间：单位秒，默认10秒
     */
    public void setIdempotentTimeOut(Integer idempotentTimeOut) {
        this.idempotentTimeOut = idempotentTimeOut;
    }

    /**
     * 获取创建时间
     *
     * @return createTime - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取描述
     *
     * @return remark - 描述
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置描述
     *
     * @param remark 描述
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取预留元数据
     *
     * @return metaData - 预留元数据
     */
    public String getMetaData() {
        return metaData;
    }

    /**
     * 设置预留元数据
     *
     * @param metaData 预留元数据
     */
    public void setMetaData(String metaData) {
        this.metaData = metaData;
    }

    /**
     * 获取更新时间
     *
     * @return handleTime - 更新时间
     */
    public Date getHandleTime() {
        return handleTime;
    }

    /**
     * 设置更新时间
     *
     * @param handleTime 更新时间
     */
    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    /**
     * 获取更新人
     *
     * @return handler - 更新人
     */
    public String getHandler() {
        return handler;
    }

    /**
     * 设置更新人
     *
     * @param handler 更新人
     */
    public void setHandler(String handler) {
        this.handler = handler;
    }
}
