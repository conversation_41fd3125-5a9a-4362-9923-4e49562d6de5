package com.sinoair.ceos.gateway.domain.model;

import javax.persistence.*;
import java.util.Date;

/**
 * 表名：gw_update_log
*/
@Table(name = "gw_update_log")
public class GwUpdateLog {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 表名
     */
    @Column(name = "table_name")
    private String tableName;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新前
     */
    @Column(name = "update_before")
    private String updateBefore;

    /**
     * 更新后
     */
    @Column(name = "update_after")
    private String updateAfter;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取表名
     *
     * @return tableName - 表名
     */
    public String getTableName() {
        return tableName;
    }

    /**
     * 设置表名
     *
     * @param tableName 表名
     */
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    /**
     * 获取更新时间
     *
     * @return updateTime - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新人
     *
     * @return updateUser - 更新人
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * 设置更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * 获取更新前
     *
     * @return updateBefore - 更新前
     */
    public String getUpdateBefore() {
        return updateBefore;
    }

    /**
     * 设置更新前
     *
     * @param updateBefore 更新前
     */
    public void setUpdateBefore(String updateBefore) {
        this.updateBefore = updateBefore;
    }

    /**
     * 获取更新后
     *
     * @return updateAfter - 更新后
     */
    public String getUpdateAfter() {
        return updateAfter;
    }

    /**
     * 设置更新后
     *
     * @param updateAfter 更新后
     */
    public void setUpdateAfter(String updateAfter) {
        this.updateAfter = updateAfter;
    }
}
