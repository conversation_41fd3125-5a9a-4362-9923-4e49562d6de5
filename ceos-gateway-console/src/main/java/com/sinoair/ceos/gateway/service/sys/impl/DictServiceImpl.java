package com.sinoair.ceos.gateway.service.sys.impl;

import com.sinoair.ceos.gateway.dao.GwDictMapper;
import com.sinoair.ceos.gateway.domain.model.GwDict;
import com.sinoair.ceos.gateway.service.sys.DictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: 大雄
 */
@Service
public class DictServiceImpl implements DictService {

    @Autowired
    private GwDictMapper gwDictMapper;

    /**
     * 查询字典信息
     *
     * @param groupCode
     * @return
     */
    @Override
    public GwDict selectByGroup(String groupCode) {
        return this.gwDictMapper.selectByGroupCode(groupCode);
    }
}
