package com.sinoair.ceos.gateway.service.sys;

import com.sinoair.ceos.gateway.domain.dto.sys.*;
import com.sinoair.ceos.gateway.domain.model.GwAppInfo;

import java.util.List;

/**
 * @Author: daxiong
 */
public interface AppService {

    /**
     * 查询应用信息
     * @param appListParam
     * @return
     */
    List<GwAppInfo> selectListByParam(AppListParam appListParam);

    /**
     * 保存应用信息
     * @param appSaveParam
     */
    GwAppInfo saveAppInfo(AppSaveParam appSaveParam);

    /**
     * 重置secret
     * @param appSecretResetParam
     */
    String resetSecret(AppSecretResetParam appSecretResetParam);

    /**
     * 查询应用绑定的api资源
     * @return
     */
    List<AppPermission> selectAppResourcesList(AppPermissionListParam appPermissionParam);

    /**
     * 保存授权信息
     * @param appPermissioinSaveParam
     */
    void saveAppPermission(AppPermissioinSaveParam appPermissioinSaveParam);

}
