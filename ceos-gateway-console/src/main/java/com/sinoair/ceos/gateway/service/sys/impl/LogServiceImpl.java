package com.sinoair.ceos.gateway.service.sys.impl;

import com.sinoair.ceos.gateway.common.mongo.GatewayApiLogEntity;
import com.sinoair.ceos.gateway.common.util.StringUtil;
import com.sinoair.ceos.gateway.service.sys.LogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: daxiong
 */
@Service
public class LogServiceImpl implements LogService {

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 查询日志信息
     *
     * @return
     */
    @Override
    public List<GatewayApiLogEntity> selectApiLog(String messageId,String searchCode,String searchType) {
        if (StringUtil.isEmpty(messageId) && StringUtil.isEmpty(searchCode)){
            return new ArrayList<>();
        }else{
            Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
            Query query = new Query();
            query.with(sort);
            if (!StringUtil.isEmpty(messageId)){
                query.addCriteria(
                        new Criteria().and("messageId").is(messageId)
                );
            }
            if (!StringUtil.isEmpty(searchCode)){
                query.addCriteria(
                        new Criteria().and("searchCode").is(searchCode)
                );
            }
            if (!StringUtil.isEmpty(searchType)){
                query.addCriteria(
                        new Criteria().and("searchType").is(searchType)
                );
            }
            // 最多300条
            Pageable pageable = PageRequest.of(0, 300);
            return this.mongoTemplate.find(query.with(pageable), GatewayApiLogEntity.class);
        }
    }
}
