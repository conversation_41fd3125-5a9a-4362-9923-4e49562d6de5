package com.sinoair.ceos.gateway.dao;

import com.sinoair.ceos.gateway.core.config.MyMapper;
import com.sinoair.ceos.gateway.domain.model.GwDict;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface GwDictMapper extends MyMapper<GwDict> {

    /**
     * 根据CODE查询资源信息
     * @param groupCode
     * @return
     */
    GwDict selectByGroupCode(@Param("groupCode") String groupCode);

}
