package com.sinoair.ceos.gateway.dao;

import com.sinoair.ceos.gateway.core.config.MyMapper;
import com.sinoair.ceos.gateway.domain.dto.sys.AppListParam;
import com.sinoair.ceos.gateway.domain.model.GwAppInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GwAppInfoMapper extends MyMapper<GwAppInfo> {

    /**
     * 根据appKey查询应用信息
     * @param appKey
     * @return
     */
    GwAppInfo selectByAppKey(@Param("appKey") String appKey);

    /**
     * 应用列表查询
     * @param param
     * @return
     */
    List<GwAppInfo> selectListByParam(@Param("param") AppListParam param);

}
