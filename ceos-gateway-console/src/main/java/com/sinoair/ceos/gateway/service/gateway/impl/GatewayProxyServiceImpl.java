package com.sinoair.ceos.gateway.service.gateway.impl;

import com.alibaba.fastjson2.JSON;
import com.sinoair.ceos.gateway.common.dto.proxy.GatewayProxySyncInfo;
import com.sinoair.ceos.gateway.common.dto.proxy.HttpProxyInfo;
import com.sinoair.ceos.gateway.common.exception.CeosGatewayException;
import com.sinoair.ceos.gateway.common.util.DateUtil;
import com.sinoair.ceos.gateway.common.util.StringUtil;
import com.sinoair.ceos.gateway.dao.GwProxyMapper;
import com.sinoair.ceos.gateway.dao.GwProxyVersionMapper;
import com.sinoair.ceos.gateway.domain.dto.sys.ProxyListParam;
import com.sinoair.ceos.gateway.domain.dto.sys.ProxySaveParam;
import com.sinoair.ceos.gateway.domain.model.GwProxy;
import com.sinoair.ceos.gateway.domain.model.GwProxyVersion;
import com.sinoair.ceos.gateway.service.gateway.GatewayProxyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @author: 大雄
 */
@Service
public class GatewayProxyServiceImpl implements GatewayProxyService {

    @Autowired
    private GwProxyMapper gwProxyMapper;

    @Autowired
    private GwProxyVersionMapper gwProxyVersionMapper;

    /**
     * 查询代理信息
     *
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public GatewayProxySyncInfo selectProxyInfo(String linkInfo) {
        List<HttpProxyInfo> httpProxyInfos = this.gwProxyMapper.selectOnlineProxy();
        GwProxyVersion gwProxyVersion = this.gwProxyVersionMapper.selectLatest();
        GatewayProxySyncInfo gatewayProxySyncInfo = new GatewayProxySyncInfo();
        gatewayProxySyncInfo.setProxyInfos(httpProxyInfos);
        gatewayProxySyncInfo.setProxyVersion(gwProxyVersion.getProxyVersion());
        return gatewayProxySyncInfo;
    }

    /**
     * 查询代理列表信息
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public List<GwProxy> selectProxyList(ProxyListParam param) {
        return this.gwProxyMapper.selectProxyList(param);
    }

    /**
     * 保存代理信息
     *
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public GwProxy saveProxy(ProxySaveParam param) {
        if (StringUtil.isEmpty(param.getProxyCode())) {
            throw new CeosGatewayException("代理编码不能为空", "-1");
        }
        if (StringUtil.isEmpty(param.getProxyName())) {
            throw new CeosGatewayException("代理名称不能为空", "-1");
        }
        if (StringUtil.isEmpty(param.getProxyType())) {
            throw new CeosGatewayException("代理类型不能为空", "-1");
        }
        GwProxy gwProxy = new GwProxy();
        Date now = new Date();
        gwProxy.setProxyName(param.getProxyName());
        gwProxy.setProxyType(param.getProxyType());
        gwProxy.setProxyStatus(param.getProxyStatus());
        gwProxy.setTargetUrl(param.getTargetUrl());
        gwProxy.setTargetHeaders("[]");
        gwProxy.setTargetForms("[]");
        gwProxy.setTargetMethod(param.getTargetMethod());
        gwProxy.setTargetTimeout(param.getTargetTimeout());
        gwProxy.setRemark(param.getRemark());
        gwProxy.setSwitchRateLimit(param.getSwitchRateLimit());
        gwProxy.setRateLimit(param.getRateLimit());
        gwProxy.setHandler(param.getHandler());
        gwProxy.setCoder(param.getCoder());
        gwProxy.setHandleTime(now);
        gwProxy.setTargetType(param.getTargetType());
        gwProxy.setSwitchLog(param.getSwitchLog());
        GwProxy updateProxy = this.gwProxyMapper.selectByCode(param.getProxyCode());

        GatewayProxySyncInfo backupInfo = this.selectProxyInfo(null);

        if ("add".equals(param.getSaveType())){
            if (updateProxy != null){
                throw new CeosGatewayException("代理编码已存在", "-1");
            }
            gwProxy.setProxyCode(param.getProxyCode());
            gwProxy.setProxyUid(UUID.randomUUID().toString());
            gwProxy.setCreateTime(now);
            this.gwProxyMapper.insert(gwProxy);
        }else{
            gwProxy.setProxyCode(param.getProxyCode());
            gwProxy.setId(updateProxy.getId());
            gwProxy.setProxyUid(updateProxy.getProxyUid());
            this.gwProxyMapper.updateByPrimaryKey(gwProxy);
        }
        this.updateVersion(param.getHandler(), JSON.toJSONString(backupInfo),"更新配置信息");
        return gwProxy;
    }

    /**
     * 更新版本
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateVersion(String handler,String proxyInfo,String remark) {
        // 获取最高的版本
        GwProxyVersion gwProxyVersion = gwProxyVersionMapper.selectLatest();
        Date now = new Date();
        // 更新版本
        String s = DateUtil.parseDateStr(DateUtil.DATE_yyyyMMdd,new Date());
        String[] aa = gwProxyVersion.getProxyVersion().split("-");
        String newVersion;
        if (aa.length != 2){
            newVersion = s + "-1";
        }else{
            if (s.equals(aa[0])){
                newVersion = s + "-" + (Integer.valueOf(aa[1]) + 1);
            }else{
                newVersion = s + "-1";
            }
        }
        GwProxyVersion version = new GwProxyVersion();
        version.setProxyVersion(newVersion);
        version.setProxyInfo(proxyInfo);
        version.setProxyStatus(1);
        version.setHandleTime(now);
        version.setHandler(handler);
        version.setRemark(remark);
        this.gwProxyVersionMapper.insert(version);
        gwProxyVersion.setProxyStatus(0);
        gwProxyVersion.setHandleTime(now);
        this.gwProxyVersionMapper.updateByPrimaryKey(gwProxyVersion);
    }
}
