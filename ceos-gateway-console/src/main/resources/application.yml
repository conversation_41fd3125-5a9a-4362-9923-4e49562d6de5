server:
  port: 9666
  servlet:
    context-path: /gateway-console
  tomcat:
    basedir: ./tmp

spring:
  application:
    name: ceos-gateway-console
  profiles:
    active: dev

  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 20MB

mybatis:
  type-aliases-package: com.sinoair.ceos.gateway.domain.model
  mapper-locations: classpath:mapper/*.xml
  configuration:
    # 全局映射器启用缓存
    cache-enabled: true
    # 查询时，关闭关联对象即时加载以提高性能
    lazy-loading-enabled: true
    # 对于未知的SQL查询，允许返回不同的结果集以达到通用的效果
    multiple-result-sets-enabled: true
    # 允许使用列标签代替列名
    use-column-label: true
    # 不允许使用自定义的主键值(比如由程序生成的UUID 32位编码作为键值)，数据表的PK生成策略将被覆盖
    use-generated-keys: false
    # 给予被嵌套的resultMap以字段-属性的映射支持 FULL,PARTIAL
    auto-mapping-behavior: partial
    # 对于批量更新操作缓存SQL以提高性能 BATCH,SIMPLE
    default-executor-type:  SIMPLE
    # 数据库超过25000秒仍未响应则超时
    # default-statement-timeout: 25000

    # Allows using RowBounds on nested statements
    safe-row-bounds-enabled: false
    # Enables automatic mapping from classic database column names A_COLUMN to camel case classic Java property names aColumn
    map-underscore-to-camel-case: true

    # MyBatis uses local cache to prevent circular references and speed up repeated nested queries. By default (SESSION) all queries executed during a session are cached. If localCacheScope=STATEMENT
    #            local session will be used just for statement execution, no data will be shared between two different calls to the same SqlSession
    local-cache-scope: session

    # Specifies the JDBC type for null values when no specific JDBC type was provided for the parameter. Some drivers require specifying the column JDBC type but others work with generic values
    #            like NULL, VARCHAR or OTHER.
    jdbc-type-for-null: 'null'
    # Specifies which Object's methods trigger a lazy load
    lazy-load-trigger-methods: equals,clone,hashCode,toString
    # 设置关联对象加载的形态，此处为按需加载字段(加载字段由SQL指 定)，不会加载关联表的所有字段，以提高性能
    aggressive-lazy-loading: false


mapper:
  mappers:
    - com.sinoair.ceos.gateway.core.config.MyMapper
  not-empty: false
  identity: MYSQL

pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

