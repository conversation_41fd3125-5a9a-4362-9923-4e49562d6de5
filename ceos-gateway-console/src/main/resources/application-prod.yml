spring:
  devtools:
    livereload:
      enabled: false
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *****************************************************************************************************************************************************
      username: ceos_gateway
      password: SinoAir.Gateway@2022!
      filters: stat,wall
      initial-size: 5
      min-idle: 5
      max-active: 200
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      use-global-data-source-stat: true
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
  data:
    mongodb:
      auto-index-creation: false
      # 连接副本集，slaveOk=true 表示开启副本节点的读支持，可实现读写分离，connect=replicaSet 表示自动到副本集中选择读写的主机，replicaSet=myrs 用来指定副本集的名称
      # uri: ***********************************************************************************************
      uri: *****************************************************************************************************

ceos-gateway:
  secret:
    link:
      app-key: MjE5NjViNGRiMWRhZWZhNTYwOGM3MWQ2YmRmZjU2MTQ=
      app-secret: ZTc0ZWM5ZTE1NWE5MTZkZTlmMjJjZDAzMTJiMjM1ZmE=
    devops:
      app-key: MDI2ZTM1YzIwNGU3YzEwYTVlNWI2NGU2ZDkzODEyMjQ=
      app-secret: MDYzYTBlNjYxZWEyMmE1YjVlYzc4ZjI0NTQ2M2M0MWM=
