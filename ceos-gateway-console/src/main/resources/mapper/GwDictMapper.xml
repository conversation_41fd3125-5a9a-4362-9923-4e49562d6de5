<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.ceos.gateway.dao.GwDictMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.ceos.gateway.domain.model.GwDict">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_code" jdbcType="VARCHAR" property="groupCode" />
    <result column="group_describe" jdbcType="VARCHAR" property="groupDescribe" />
    <result column="dict_status" jdbcType="INTEGER" property="dictStatus" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="dict_data" jdbcType="LONGVARCHAR" property="dictData" />
  </resultMap>
  <select id="selectByGroupCode" resultMap="BaseResultMap">
    select * from gw_dict
    where 1=1
    and group_code = #{groupCode}
  </select>
</mapper>
