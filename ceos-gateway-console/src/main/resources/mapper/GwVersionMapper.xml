<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.ceos.gateway.dao.GwVersionMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.ceos.gateway.domain.model.GwVersion">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gw_version" jdbcType="VARCHAR" property="gwVersion" />
    <result column="gw_status" jdbcType="INTEGER" property="gwStatus" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="handler" jdbcType="VARCHAR" property="handler" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="gw_info" jdbcType="LONGVARCHAR" property="gwInfo" />
  </resultMap>
  <select id="selectLatest" resultMap="BaseResultMap">
    select * from gw_version
    where gw_status = 1
    order by id desc
    limit 1
  </select>
</mapper>
