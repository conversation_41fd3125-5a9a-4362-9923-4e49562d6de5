<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.ceos.gateway.dao.GwAppInfoMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.ceos.gateway.domain.model.GwAppInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_key" jdbcType="VARCHAR" property="appKey" />
    <result column="app_secret" jdbcType="VARCHAR" property="appSecret" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="app_description" jdbcType="VARCHAR" property="appDescription" />
    <result column="app_status" jdbcType="INTEGER" property="appStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="meta_data" jdbcType="LONGVARCHAR" property="metaData" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="handler" jdbcType="VARCHAR" property="handler" />
  </resultMap>
  <select id="selectByAppKey" resultMap="BaseResultMap">
    select *
    from gw_app_info
    where 1=1
    and app_key = #{appKey}
  </select>
  <select id="selectListByParam" resultMap="BaseResultMap">
      select *
      from gw_app_info
      where 1=1
      <if test="param.appName != null and param.appName != '' ">
        and app_name like CONCAT('%',#{param.appName},'%')
      </if>
    <if test="param.appKey != null and param.appKey != '' ">
      and app_key = #{param.appKey}
    </if>
    order by id desc
  </select>
</mapper>
