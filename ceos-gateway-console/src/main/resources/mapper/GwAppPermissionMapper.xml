<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.ceos.gateway.dao.GwAppPermissionMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.ceos.gateway.domain.model.GwAppPermission">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_key" jdbcType="VARCHAR" property="appKey" />
    <result column="res_code" jdbcType="VARCHAR" property="resCode" />
    <result column="switch_status" jdbcType="INTEGER" property="switchStatus" />
    <result column="switch_signature" jdbcType="INTEGER" property="switchSignature" />
    <result column="switch_timestamp" jdbcType="INTEGER" property="switchTimestamp" />
    <result column="switch_request_body" jdbcType="INTEGER" property="switchRequestBody" />
    <result column="switch_rate_limit" jdbcType="INTEGER" property="switchRateLimit" />
    <result column="rate_limit" jdbcType="INTEGER" property="rateLimit" />
    <result column="switch_log" jdbcType="INTEGER" property="switchLog" />
    <result column="log_days" jdbcType="INTEGER" property="logDays" />
    <result column="switch_idempotent" jdbcType="INTEGER" property="switchIdempotent" />
    <result column="idempotent_time_out" jdbcType="INTEGER" property="idempotentTimeOut" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="meta_data" jdbcType="LONGVARCHAR" property="metaData" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="handler" jdbcType="VARCHAR" property="handler" />
  </resultMap>
  <resultMap id="TableResultMap" type="com.sinoair.ceos.gateway.domain.dto.sys.AppPermission" extends="BaseResultMap">
    <result column="res_name" jdbcType="VARCHAR" property="resName" />
    <result column="res_app" jdbcType="VARCHAR" property="resApp" />
    <result column="res_status" jdbcType="INTEGER" property="resStatus" />
    <result column="timeout" jdbcType="BIGINT" property="timeout" />
    <result column="api_doc_url" jdbcType="VARCHAR" property="apiDocUrl" />
  </resultMap>
  <select id="selectTableList" resultMap="TableResultMap">
      select gap.*,gr.res_name, gr.res_app, gr.res_status, gr.timeout, gr.api_doc_url from gw_app_permission gap left join gw_resource gr on gr.res_code = gap.res_code
      where 1=1
      and gap.app_key = #{ param.appKey }
      <if test="param.search != null and param.search != ''">
        and ( gap.res_code like CONCAT('%',#{param.search},'%') or gr.res_name like  CONCAT('%',#{param.search},'%'))
      </if>
      <if test="param.resApp != '_ALL'">
        and gr.res_app = #{param.resApp}
      </if>
      order by gap.id desc
  </select>
</mapper>
