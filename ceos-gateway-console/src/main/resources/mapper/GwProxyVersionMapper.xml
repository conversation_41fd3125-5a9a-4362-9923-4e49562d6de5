<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.ceos.gateway.dao.GwProxyVersionMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.ceos.gateway.domain.model.GwProxyVersion">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="proxy_version" jdbcType="VARCHAR" property="proxyVersion" />
    <result column="proxy_status" jdbcType="INTEGER" property="proxyStatus" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="handler" jdbcType="VARCHAR" property="handler" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="proxy_info" jdbcType="LONGVARCHAR" property="proxyInfo" />
  </resultMap>
  <select id="selectLatest" resultMap="BaseResultMap">
    select * from gw_proxy_version
    where proxy_status = 1
    order by id desc
    limit 1
  </select>
</mapper>
