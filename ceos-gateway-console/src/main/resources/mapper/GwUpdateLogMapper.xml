<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.ceos.gateway.dao.GwUpdateLogMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.ceos.gateway.domain.model.GwUpdateLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_before" jdbcType="LONGVARCHAR" property="updateBefore" />
    <result column="update_after" jdbcType="LONGVARCHAR" property="updateAfter" />
  </resultMap>
</mapper>