<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.ceos.gateway.dao.GwResourceMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.ceos.gateway.domain.model.GwResource">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="res_code" jdbcType="VARCHAR" property="resCode" />
    <result column="res_name" jdbcType="VARCHAR" property="resName" />
    <result column="res_app" jdbcType="VARCHAR" property="resApp" />
    <result column="res_url" jdbcType="VARCHAR" property="resUrl" />
    <result column="res_method" jdbcType="VARCHAR" property="resMethod" />
    <result column="res_status" jdbcType="INTEGER" property="resStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="handler" jdbcType="VARCHAR" property="handler" />
    <result column="timeout" jdbcType="BIGINT" property="timeout" />
    <result column="api_doc_url" jdbcType="VARCHAR" property="apiDocUrl" />
    <result column="res_header" jdbcType="LONGVARCHAR" property="resHeader" />
    <result column="res_cookie" jdbcType="LONGVARCHAR" property="resCookie" />
    <result column="res_form_data" jdbcType="LONGVARCHAR" property="resFormData" />
    <result column="meta_data" jdbcType="LONGVARCHAR" property="metaData" />
  </resultMap>
    <select id="selectTableList" resultMap="BaseResultMap">
      select * from gw_resource
      where 1 = 1
      <if test="param.search != null and param.search != ''">
        and ( res_code like CONCAT('%',#{param.search},'%') or res_name like  CONCAT('%',#{param.search},'%'))
      </if>
      <if test="param.resApp != '_ALL'">
        and res_app = #{param.resApp}
      </if>
      order by id desc
    </select>
  <select id="selectByResCode" resultMap="BaseResultMap">
    select * from gw_resource where res_code = #{resCode}
  </select>
  <select id="selectListNotPermission" resultMap="BaseResultMap">
    SELECT
        *
    FROM
        gw_resource gr
    WHERE
        1 = 1
        AND gr.res_code NOT IN (
        SELECT
            gap.res_code
        FROM
            gw_app_permission gap
    WHERE
        gap.app_key = #{appKey})
  </select>
</mapper>
