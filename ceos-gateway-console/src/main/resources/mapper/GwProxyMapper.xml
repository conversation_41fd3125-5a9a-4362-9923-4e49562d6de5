<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.ceos.gateway.dao.GwProxyMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.ceos.gateway.domain.model.GwProxy">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="proxy_uid" jdbcType="VARCHAR" property="proxyUid" />
    <result column="proxy_code" jdbcType="VARCHAR" property="proxyCode" />
    <result column="proxy_name" jdbcType="VARCHAR" property="proxyName" />
    <result column="proxy_type" jdbcType="VARCHAR" property="proxyType" />
    <result column="proxy_status" jdbcType="INTEGER" property="proxyStatus" />
    <result column="target_url" jdbcType="VARCHAR" property="targetUrl" />
    <result column="target_type" jdbcType="VARCHAR" property="targetType" />
    <result column="target_method" jdbcType="VARCHAR" property="targetMethod" />
    <result column="target_timeout" jdbcType="INTEGER" property="targetTimeout" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="switch_rate_limit" jdbcType="INTEGER" property="switchRateLimit" />
    <result column="rate_limit" jdbcType="INTEGER" property="rateLimit" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="handler" jdbcType="VARCHAR" property="handler" />
    <result column="coder" jdbcType="VARCHAR" property="coder" />
    <result column="switch_log" jdbcType="INTEGER" property="switchLog" />
    <result column="target_headers" jdbcType="LONGVARCHAR" property="targetHeaders" />
    <result column="target_forms" jdbcType="LONGVARCHAR" property="targetForms" />
    <result column="meta_data" jdbcType="LONGVARCHAR" property="metaData" />
  </resultMap>

  <resultMap id="SyncResultMap" type="com.sinoair.ceos.gateway.common.dto.proxy.HttpProxyInfo" extends="BaseResultMap">
  </resultMap>
  <select id="selectOnlineProxy" resultMap="SyncResultMap">
    select * from gw_proxy
    where 1=1
    and proxy_status = 1
  </select>
  <select id="selectProxyList" resultMap="SyncResultMap">
    select * from gw_proxy
    where 1=1
    <if test="param.proxyName != null and param.proxyName != '' ">
      and proxy_name like concat('%',#{param.proxyName,jdbcType=VARCHAR},'%')
    </if>
    <if test="param.proxyCode != null and param.proxyCode != '' ">
      and proxy_code = #{param.proxyCode,jdbcType=VARCHAR}
    </if>
    order by id desc
  </select>
  <select id="selectByCode" resultMap="BaseResultMap">
    select *
    from gw_proxy
    where 1=1
    and proxy_code = #{proxyCode,jdbcType=VARCHAR}
  </select>

</mapper>
