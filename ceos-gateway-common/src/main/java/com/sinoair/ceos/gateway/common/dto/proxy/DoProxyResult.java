package com.sinoair.ceos.gateway.common.dto.proxy;

import com.sinoair.ceos.gateway.common.dto.rocketmq.AppLogSaveParamDto;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @Author: 大雄
 * @Date: 2024/2/28 12:55
 * @Description:
 */
public class DoProxyResult {

    private AppLogSaveParamDto logDto;

    private HttpServletResponse response;

    private String contentType;

    private int httpStatus;

    private Map<String,String> headers;

    private String content;

    public DoProxyResult() {
    }

    public DoProxyResult(AppLogSaveParamDto logDto, HttpServletResponse response, String contentType, int httpStatus, Map<String, String> headers, String content) {
        this.logDto = logDto;
        this.response = response;
        this.contentType = contentType;
        this.httpStatus = httpStatus;
        this.headers = headers;
        this.content = content;
    }

    public AppLogSaveParamDto getLogDto() {
        return logDto;
    }

    public void setLogDto(AppLogSaveParamDto logDto) {
        this.logDto = logDto;
    }

    public HttpServletResponse getResponse() {
        return response;
    }

    public void setResponse(HttpServletResponse response) {
        this.response = response;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public int getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(int httpStatus) {
        this.httpStatus = httpStatus;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
