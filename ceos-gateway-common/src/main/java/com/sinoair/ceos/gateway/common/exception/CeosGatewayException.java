package com.sinoair.ceos.gateway.common.exception;

/**
 * @Author: daxiong
 */
public class CeosGatewayException extends RuntimeException {

    private String code;


    /**
     * Constructs a new runtime exception with the specified detail message.
     * The cause is not initialized, and may subsequently be initialized by a
     * call to {@link #initCause}.
     *
     * @param message the detail message. The detail message is saved for
     *                later retrieval by the {@link #getMessage()} method.
     */
    public CeosGatewayException(String message, String code) {
        super(message);
        this.code = code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
