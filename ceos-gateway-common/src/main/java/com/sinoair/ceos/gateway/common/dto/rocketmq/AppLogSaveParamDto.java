package com.sinoair.ceos.gateway.common.dto.rocketmq;

/**
 * @Author: 大雄
 * @Date: 2023/2/21 14:02
 * @Description: 操作日志存储实体
 */
public class AppLogSaveParamDto {

    /**
     * 日志id，有则存，无则自动生成
     */
    private String sysId;

    /**
     * 应用code  ceos gtms
     */
    private String appCode;

    /**
     * 应用名称  官网 管理中心 移动端
     */
    private String appName;

    /**
     * 应用部署信息
     */
    private String appDeployInfo;

    /**
     * 业务id 各种主键等
     */
    private String bizId;

    /**
     * 业务code 单号等业务号段，查询用
     */
    private String bizCode;

    /**
     * 业务类型 标注大类，方便分类查询，自定义
     */
    private String bizType;

    /**
     * 功能描述
     */
    private String function;

    /**
     * 请求url
     */
    private String url;

    /**
     * 请求参数
     */
    private String request;

    /**
     * 响应信息
     */
    private String response;

    /**
     * 日志级别
     * DEBUG：详细的信息,通常只出现在诊断问题上
     * INFO：确认一切按预期运行
     * WARNING：一个迹象表明,一些意想不到的事情发生了,或表明一些问题在不久的将来(例如。磁盘空间低”)。这个软件还能按预期工作。
     * ERROR：更严重的问题,软件没能执行一些功能
     * CRITICAL：一个严重的错误,这表明程序本身可能无法继续运行
     */
    private String logLevel;

    /**
     * 堆栈信息
     */
    private String errStack;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间 毫秒时间戳
     */
    private Long operationTime;

    /**
     * 操作内容
     */
    private String operatorInfo;

    /**
     * 扩展数据
     */
    private String extMeta;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态：SUCCESS，ERROR
     */
    private String logStatus;

    /**
     * 请求时间
     */
    private Long requestTime;

    /**
     * 响应时间
     */
    private Long responseTime;

    /**
     * 处理时长
     */
    private Long processingTime;

    /**
     * 请求头
     */
    private String requestHeader;

    /**
     * 响应头
     */
    private String responseHeader;

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppDeployInfo() {
        return appDeployInfo;
    }

    public void setAppDeployInfo(String appDeployInfo) {
        this.appDeployInfo = appDeployInfo;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getFunction() {
        return function;
    }

    public void setFunction(String function) {
        this.function = function;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getLogLevel() {
        return logLevel;
    }

    public void setLogLevel(String logLevel) {
        this.logLevel = logLevel;
    }

    public String getErrStack() {
        return errStack;
    }

    public void setErrStack(String errStack) {
        this.errStack = errStack;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Long operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperatorInfo() {
        return operatorInfo;
    }

    public void setOperatorInfo(String operatorInfo) {
        this.operatorInfo = operatorInfo;
    }

    public String getExtMeta() {
        return extMeta;
    }

    public void setExtMeta(String extMeta) {
        this.extMeta = extMeta;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLogStatus() {
        return logStatus;
    }

    public void setLogStatus(String logStatus) {
        this.logStatus = logStatus;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public Long getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Long requestTime) {
        this.requestTime = requestTime;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }

    public Long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(Long processingTime) {
        this.processingTime = processingTime;
    }

    public String getRequestHeader() {
        return requestHeader;
    }

    public void setRequestHeader(String requestHeader) {
        this.requestHeader = requestHeader;
    }

    public String getResponseHeader() {
        return responseHeader;
    }

    public void setResponseHeader(String responseHeader) {
        this.responseHeader = responseHeader;
    }
}
