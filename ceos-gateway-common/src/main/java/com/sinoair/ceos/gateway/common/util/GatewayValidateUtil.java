package com.sinoair.ceos.gateway.common.util;

import com.sinoair.ceos.gateway.common.exception.CeosGatewayException;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 大雄
 */
public class GatewayValidateUtil {

    public static String readRequestParam(HttpServletRequest request,String rAppKey,String rAppSecret,String rResCode){
        // 请求方时间戳 毫秒时间戳
        String timestamp = request.getHeader("Timestamp");
        // 资源code
        String resourceCode = request.getHeader("Resource-Code");
        // appKey 接入id
        String appKey = request.getHeader("App-Key");
        // 签名
        String signature = request.getHeader("Signature");

        if (!rResCode.equals(resourceCode)){
            throw new CeosGatewayException("资源代码无效","-1");
        }
        if (StringUtil.isEmpty(timestamp)){
            throw new CeosGatewayException("时间戳无效","-1");
        }
        if (!rAppKey.equals(appKey)){
            throw new CeosGatewayException("appKey无效","-1");
        }
        String readRequest = HttpUtil.readRequest(request);
        boolean b = SignatureUtil.checkSign(signature, appKey, rAppSecret, resourceCode, readRequest, Long.valueOf(timestamp));
        if (b){
            return readRequest;
        }else{
            throw new CeosGatewayException("签名失败","-1");
        }
    }

}
