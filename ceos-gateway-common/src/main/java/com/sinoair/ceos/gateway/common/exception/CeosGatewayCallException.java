package com.sinoair.ceos.gateway.common.exception;

import com.sinoair.ceos.gateway.common.dto.app.AppResource;

/**
 * @Author: daxiong
 */
public class CeosGatewayCallException extends RuntimeException {

    private AppResource appResource;

    /**
     * Constructs a new runtime exception with the specified detail message.
     * The cause is not initialized, and may subsequently be initialized by a
     * call to {@link #initCause}.
     *
     * @param message the detail message. The detail message is saved for
     *                later retrieval by the {@link #getMessage()} method.
     */
    public CeosGatewayCallException(String message) {
        super(message);
    }

    /**
     * Constructs a new runtime exception with the specified detail message.
     * The cause is not initialized, and may subsequently be initialized by a
     * call to {@link #initCause}.
     *
     * @param message the detail message. The detail message is saved for
     *                later retrieval by the {@link #getMessage()} method.
     */
    public CeosGatewayCallException(String message, AppResource appResource) {
        super(message);
        this.appResource = appResource;
    }

    public AppResource getAppResource() {
        return appResource;
    }

    public void setAppResource(AppResource appResource) {
        this.appResource = appResource;
    }
}
