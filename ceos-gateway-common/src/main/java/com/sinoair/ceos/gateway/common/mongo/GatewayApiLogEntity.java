package com.sinoair.ceos.gateway.common.mongo;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * @Author: daxiong
 */
// 所属集合
@Document(collection = "ceos_gateway_api_log")
public class GatewayApiLogEntity {

    @Id
    private String id;

    /**
     * 消息id
     */
    @Indexed(unique = true,useGeneratedName = true)
    private String messageId;

    /**
     * 消息类型 API、RPC
     */
    @Indexed(useGeneratedName = true)
    private String messageType;

    /**
     * appKey
     */
    @Indexed(useGeneratedName = true)
    private String appKey;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 资源code
     */
    @Indexed(useGeneratedName = true)
    private String resCode;

    /**
     * 资源名称
     */
    private String resName;

    /**
     * 请求地址
     */
    private String apiUrl;


    /**
     * 20天过期
     */
    //@Indexed(useGeneratedName = true,expireAfterSeconds = 1_728_000)
    @Indexed(useGeneratedName = true,expireAfterSeconds = 5_184_000)
    private Date createTime;

    /**
     * 创建年月 2022-05
     */
    @Indexed(useGeneratedName = true)
    private String yearMonth;

    /**
     * 创建年 2022
     */
    @Indexed(useGeneratedName = true)
    private String year;

    /**
     * 创建月 05
     */
    @Indexed(useGeneratedName = true)
    private String month;

    /**
     * 创建日 05
     */
    @Indexed(useGeneratedName = true)
    private String day;

    /**
     * 请求时间
     */
    private Date startTime;

    /**
     * 请求结束时间
     */
    private Date endTime;

    /**
     * 响应时间
     */
    private Long responseTime;

    /**
     * 请求体
     */
    private String requestBody;

    /**
     * 请求头
     */
    private String requestHeader;

    /**
     * rpc请求header
     */
    private String rpcHeader;

    /**
     * 响应值
     */
    private String responseBody;

    /**
     * 资源内容
     */
    private String resourceInfo;

    /**
     * 业务查询code
     */
    // @Indexed(useGeneratedName = true)
    private String searchCode;

    /**
     * 业务查询类型
     */
    // @Indexed(useGeneratedName = true)
    private String searchType;

    /**
     * 来源：MQ，CEOS-GATEWAY
     */
    private String source;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }

    public String getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }

    public String getRpcHeader() {
        return rpcHeader;
    }

    public void setRpcHeader(String rpcHeader) {
        this.rpcHeader = rpcHeader;
    }

    public String getResponseBody() {
        return responseBody;
    }

    public void setResponseBody(String responseBody) {
        this.responseBody = responseBody;
    }

    public String getResourceInfo() {
        return resourceInfo;
    }

    public void setResourceInfo(String resourceInfo) {
        this.resourceInfo = resourceInfo;
    }

    public String getRequestHeader() {
        return requestHeader;
    }

    public void setRequestHeader(String requestHeader) {
        this.requestHeader = requestHeader;
    }

    public String getSearchCode() {
        return searchCode;
    }

    public void setSearchCode(String searchCode) {
        this.searchCode = searchCode;
    }

    public String getSearchType() {
        return searchType;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
}
