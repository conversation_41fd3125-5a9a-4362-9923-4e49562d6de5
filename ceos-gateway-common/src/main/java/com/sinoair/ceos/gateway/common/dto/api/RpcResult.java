package com.sinoair.ceos.gateway.common.dto.api;

import com.sinoair.ceos.gateway.common.dto.app.AppInfo;
import com.sinoair.ceos.gateway.common.dto.app.AppResource;

import java.util.Map;

/**
 * @Author: daxiong
 */
public class RpcResult {

    private ApiResult apiResult;

    private AppResource appResource;

    private Map<String,String> rpcHeader;

    private Map<String,String> responseHeader;

    private AppInfo appInfo;

    public RpcResult(ApiResult apiResult, AppResource appResource, Map<String, String> rpcHeader,Map<String, String> responseHeader) {
        this.apiResult = apiResult;
        this.appResource = appResource;
        this.rpcHeader = rpcHeader;
        this.responseHeader = responseHeader;
    }

    public ApiResult getApiResult() {
        return apiResult;
    }

    public void setApiResult(ApiResult apiResult) {
        this.apiResult = apiResult;
    }

    public AppResource getAppResource() {
        return appResource;
    }

    public void setAppResource(AppResource appResource) {
        this.appResource = appResource;
    }

    public Map<String, String> getRpcHeader() {
        return rpcHeader;
    }

    public void setRpcHeader(Map<String, String> rpcHeader) {
        this.rpcHeader = rpcHeader;
    }

    public Map<String, String> getResponseHeader() {
        return responseHeader;
    }

    public void setResponseHeader(Map<String, String> responseHeader) {
        this.responseHeader = responseHeader;
    }

    public AppInfo getAppInfo() {
        return appInfo;
    }

    public void setAppInfo(AppInfo appInfo) {
        this.appInfo = appInfo;
    }
}
