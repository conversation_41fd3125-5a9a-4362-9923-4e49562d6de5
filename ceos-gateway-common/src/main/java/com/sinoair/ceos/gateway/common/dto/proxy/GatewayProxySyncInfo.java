package com.sinoair.ceos.gateway.common.dto.proxy;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 大雄
 */
public class GatewayProxySyncInfo implements Serializable {
    private static final long serialVersionUID = -4091758175081016876L;

    /**
     * 代理版本
     */
    private String proxyVersion;

    /**
     * 代理内容
     */
    private List<HttpProxyInfo> proxyInfos;

    public String getProxyVersion() {
        return proxyVersion;
    }

    public void setProxyVersion(String proxyVersion) {
        this.proxyVersion = proxyVersion;
    }

    public List<HttpProxyInfo> getProxyInfos() {
        return proxyInfos;
    }

    public void setProxyInfos(List<HttpProxyInfo> proxyInfos) {
        this.proxyInfos = proxyInfos;
    }
}
