package com.sinoair.ceos.gateway.common.constant;

/**
 * @Author: daxiong
 */
public interface GatewayConstant {

    /**
     * LINK_APP_KEY
     */
    String GATEWAY_LINK_APP_KEY = "YzQwMmQ0YTJmMGM0OGYwNDZhODY0MjhiZGIxM2UyMTc=";
    /**
     * LINK_CONSTANT
     */
    String GATEWAY_LINK_APP_SECRET = "NmViNjI2M2UwN2Y2MDAwMjU3MzEyZjNmMWFhMWE4YzA=";
    /**
     * 控制台命令行
     */
    String DEVOPS_APP_KEY = "Mjk3NTljNDNjYzJhOTE2ZGIxMTM2NWEzZmNlZjQzOTQ=";
    /**
     * 控制台密码
     */
    String DEVOPS_APP_SECRET = "N2FjZGI2NzIzYjRlNGZjYzRjZGVmM2RlNDQ3NDYwNDY=";

    /**
     * 限流器
     */
    String REDIS_TEMPLATE_PREFIX = "ceos:gateway:rate-limit:";

    /**
     * 幂等
     */
    String REDIS_IDEMPOTENT_LOCK_PREFIX = "ceos:gateway:idempotent:lock:";

}
