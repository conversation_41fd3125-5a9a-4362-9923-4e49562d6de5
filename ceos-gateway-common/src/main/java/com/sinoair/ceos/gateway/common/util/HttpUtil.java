package com.sinoair.ceos.gateway.common.util;

import com.alibaba.fastjson2.JSONObject;
import com.sinoair.ceos.gateway.common.dto.api.HttpResultDto;
import com.sinoair.ceos.gateway.common.dto.app.AppResource;
import com.sinoair.ceos.gateway.common.exception.CeosGatewayCallException;
import org.apache.http.Header;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;

/**
 * @Author: daxiong
 */
public class HttpUtil {

    private static Logger LOGGER = LoggerFactory.getLogger(HttpUtil.class);
    // private static CloseableHttpClient httpClient;
    // private static HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
    private static RequestConfig DEFAULT_REQUEST_CONFIG = RequestConfig.custom()
            .setSocketTimeout(60 * 1000)
            .setConnectTimeout(60 * 1000).build();
//    static {
//        PoolingHttpClientConnectionManager connectionManager = null;
//        try {
//            //https config
//            TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;
//            SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
//                    .loadTrustMaterial(null, acceptingTrustStrategy)
//                    .build();
//
//            SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext
//                    ,null, null, NoopHostnameVerifier.INSTANCE);
//
//            Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
//                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
//                    .register("https", csf)
//                    .build();
//
//            connectionManager = new PoolingHttpClientConnectionManager(registry);
//            //最大连接数3000
//            connectionManager.setMaxTotal(3000);
//            //路由链接数400
//            connectionManager.setDefaultMaxPerRoute(400);
//
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//
//        RequestConfig requestConfig = RequestConfig.custom()
//                .setSocketTimeout(60000)
//                .setConnectTimeout(60000)
//                .setConnectionRequestTimeout(10000)
//                .build();
//        httpClient = HttpClients.custom().setDefaultRequestConfig(requestConfig)
//                .setConnectionManager(connectionManager)
//                .evictExpiredConnections()
//                .evictIdleConnections(30, TimeUnit.SECONDS)
//                .build();
//        requestFactory.setHttpClient(httpClient);
//
//    }

//    private static CloseableHttpClient getClient(){
//        return httpClient;
//    }



    /**
     * 远程调用
     * @param url 请求地址
     * @param header 请求头
     * @param cookie cookie
     * @param formBody 表单
     * @param requestBody 请求体
     * @param timeout 超时时间
     * @return
     */
    public static HttpResultDto post(String url,
                                     Map<String,String> header,
                                     Map<String,String> cookie,
                                     Map<String,String> formBody,
                                     String requestBody,
                                     Integer timeout
                              ){
        CloseableHttpClient client = null;
        CloseableHttpResponse httpResponse = null;
        Map<String,String> responseHeader = new HashMap<>();
        String result = null;
        try {
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.setCharset(Charset.forName("UTF-8"));

            //form表单配置
            if (formBody != null && formBody.keySet().size() > 0){
              /*  List<BasicNameValuePair> pairList = new ArrayList<>();
                for (String key : formBody.keySet()){
                    pairList.add(new BasicNameValuePair(key,formBody.get(key)));
                }
                post.setEntity(new UrlEncodedFormEntity(pairList, "utf-8"));*/

                List<NameValuePair> nvps = new ArrayList<>();
                for (String key : formBody.keySet()){
                    nvps.add(new BasicNameValuePair(key,formBody.get(key)));
                }
                //post.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));
                uriBuilder.setParameters(nvps);
            }
            HttpPost post = new HttpPost(uriBuilder.build());
            // 配置超时
            if (timeout == null || timeout <= 0){
                post.setConfig(DEFAULT_REQUEST_CONFIG);
                HttpClients.createDefault();
                client = HttpClients.custom().setDefaultRequestConfig(DEFAULT_REQUEST_CONFIG).build();
            }else{
                RequestConfig requestConfig = RequestConfig.custom()
                        .setSocketTimeout(timeout)
                        .setConnectTimeout(timeout).build();
                post.setConfig(requestConfig);
                client = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();
            }
            //Header配置
            if (header != null && header.keySet().size() > 0){
                for (String key: header.keySet()){
                    if (key != null){
                        post.addHeader(key, header.get(key));
                    }
                }
            }
            //Cookie配置
            if (cookie != null && cookie.keySet().size() > 0){
                StringBuilder stringBuilder = new StringBuilder();
                for (String key : cookie.keySet()){
                    stringBuilder.append(key.trim()).append("=").append(cookie.get(cookie.get(key))).append("; ");
                }
                post.addHeader("Cookie",stringBuilder.toString());
            }
            //请求体配置
            if (requestBody != null){
                StringEntity entity = new StringEntity(requestBody, "UTF-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                post.setEntity(entity);
            }
            //执行
            httpResponse = client.execute(post);

            result = EntityUtils.toString(httpResponse.getEntity(),"UTF-8");
            EntityUtils.consume(httpResponse.getEntity());

            Header[] allHeaders = httpResponse.getAllHeaders();
            for (Header h : allHeaders){
                responseHeader.put(h.getName(),h.getValue());
            }
            if (HttpStatus.OK.value() != httpResponse.getStatusLine().getStatusCode()){
                String error = uriBuilder.build().toString() +
                        "\r\n" +
                        result;
                LOGGER.error(error);
                throw new CeosGatewayCallException("远程调用异常：" + uriBuilder.build().toString()+ "  " + httpResponse.getStatusLine().getStatusCode());
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage(),e);
            if (e.getMessage().contains("Read timed out")){
                String uuid = UUID.randomUUID().toString();
                StringBuilder error = new StringBuilder();
                error.append("【").append(uuid).append("】请求超时：").append(url).append("\r\n");
                if (StringUtil.isNotEmpty(requestBody)){
                    error.append("【").append(uuid).append("】请求体参数：").append(requestBody).append("\r\n");
                }
                if (formBody != null){
                    error.append("【").append(uuid).append("】FORM参数：").append(JSONObject.toJSONString(formBody)).append("\r\n");
                }
                LOGGER.error(error.toString());
            }
            throw new CeosGatewayCallException(e.getMessage());
        }finally {
            if (httpResponse != null){
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    LOGGER.error(e.getMessage(),e);
                }
            }
            if (client != null){
                try {
                    client.close();
                } catch (IOException e) {
                    LOGGER.error(e.getMessage(),e);
                }
            }
        }
        HttpResultDto httpResultDto = new HttpResultDto();
        httpResultDto.setResponseData(result);
        httpResultDto.setResponseHeader(responseHeader);
        return httpResultDto;
    }

    /**
     * 读取request流
     *
     * @param request
     * @return
     * @throws IOException
     */
    public static String readRequest(HttpServletRequest request){
        StringBuilder builder = new StringBuilder();
        String line = null;
        BufferedReader reader = null;
        try {
            reader = request.getReader();
            char[] charBuffer = new char[128];
            int bytesRead;
            while ( (bytesRead = reader.read(charBuffer)) != -1){
                builder.append(charBuffer, 0, bytesRead);
            }
        } catch (Exception e) {
            // e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }finally {
            if (reader != null){
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return builder.toString();
    }

    /**
     * 远程调用
     * @param appResource appReource
     * @param header 请求头
     * @param requestBody 请求体
     * @return
     */
    public static String post(AppResource appResource,
                              Map<String,String> header,
                              String requestBody
    ){
        CloseableHttpResponse httpResponse = null;
        CloseableHttpClient client = HttpClients.custom().build();
        String result = null;
        try {
            URIBuilder uriBuilder = new URIBuilder(appResource.getResUrl());
            uriBuilder.setCharset(Charset.forName("UTF-8"));

            //form表单配置
            if (appResource.getResFormData() != null && appResource.getResFormData().keySet().size() > 0){
                List<NameValuePair> nvps = new ArrayList<>();
                for (String key : appResource.getResFormData().keySet()){
                    nvps.add(new BasicNameValuePair(key,appResource.getResFormData().get(key)));
                }
                uriBuilder.setParameters(nvps);
            }
            HttpPost post = new HttpPost(uriBuilder.build());
            // 配置超时
            if (appResource.getTimeout() == null || appResource.getTimeout() <= 0){
                post.setConfig(DEFAULT_REQUEST_CONFIG);
            }else{
                RequestConfig requestConfig = RequestConfig.custom()
                        .setSocketTimeout(appResource.getTimeout())
                        .setConnectTimeout(appResource.getTimeout()).build();
                post.setConfig(requestConfig);
            }
            //Header配置
            if (header != null && header.keySet().size() > 0){
                for (String key: header.keySet()){
                    if (key != null){
                        post.addHeader(key, header.get(key));
                    }
                }
            }
            //Cookie配置
            if (appResource.getResCookie() != null && appResource.getResCookie().keySet().size() > 0){
                StringBuilder stringBuilder = new StringBuilder();
                for (String key : appResource.getResCookie().keySet()){
                    stringBuilder.append(key.trim()).append("=").append(appResource.getResCookie().get(key)).append("; ");
                }
                post.addHeader("Cookie",stringBuilder.toString());
            }
            //请求体配置
            if (requestBody != null){
                StringEntity entity = new StringEntity(requestBody, "UTF-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                post.setEntity(entity);
            }
            //执行
            httpResponse = client.execute(post);
            result = EntityUtils.toString(httpResponse.getEntity(),"UTF-8");
            if (HttpStatus.OK.value() != httpResponse.getStatusLine().getStatusCode()){
                String error = uriBuilder.build().toString() +
                        "\r\n" +
                        result;
                LOGGER.error(error);
                throw new CeosGatewayCallException("远程调用异常：" + uriBuilder.build().toString()+ "  " + httpResponse.getStatusLine().getStatusCode(),appResource);
            }

        } catch (Exception e) {
            // e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
            throw new CeosGatewayCallException(e.getMessage(),appResource);
        }finally {
            if (httpResponse != null){
                try {
                    httpResponse.close();
                } catch (IOException e) {
                    // e.printStackTrace();
                    LOGGER.error(e.getMessage(),e);
                }
            }
            if (client != null){
                try {
                    client.close();
                } catch (IOException e) {
                    LOGGER.error(e.getMessage(),e);
                }
            }
        }
        return result;
    }

    public static void writeResponse(HttpServletResponse response,
                                     String contentType,
                                     int httpStatus,
                                     Map<String,String> headers,
                                     String content){
        try {
            response.setCharacterEncoding("utf-8");
            PrintWriter writer = response.getWriter();
            response.setStatus(httpStatus);
            response.setContentType(contentType);
            if (headers != null){
                headers.keySet().forEach(key -> {
                    response.addHeader(key,headers.get(key));
                });
            }
            writer.write(content);
            writer.flush();
            writer.close();
        } catch (IOException e) {
            // e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }
    }

    /**
     * url解码
     * @param decodeStr
     * @return
     */
    public static String urlEncode(String decodeStr){
        try {
            return URLEncoder.encode(decodeStr,"UTF-8");
        } catch (UnsupportedEncodingException e) {
            // e.printStackTrace();
            LOGGER.error(e.getMessage(),e);
        }
        return null;
    }
}
