package com.sinoair.ceos.gateway.common.util;

import com.sinoair.ceos.gateway.common.dto.api.ApiResult;

/**
 * @Author: daxiong
 */
public class ApiResultBuilder {

    private static final String SUCCESS_CODE = "200";
    private static final String SUCCESS = "Success";

    private String messageId;

    private String code;

    private String message;

    private Object result;

    private ApiResultBuilder(){}


    public static ApiResultBuilder createBuilder(){
        return new ApiResultBuilder();
    }

    public ApiResultBuilder messageId(String messageId){
        this.messageId = messageId;
        return this;
    }

    public ApiResultBuilder code(String code){
        this.code = code;
        return this;
    }

    public ApiResultBuilder message(String message){
        this.message = message;
        return this;
    }

    public ApiResultBuilder result(Object result){
        this.result = result;
        return this;
    }

    public ApiResultBuilder success(Object result){
        return this.code(SUCCESS_CODE).message(SUCCESS).result(result);
    }

    public ApiResultBuilder success(String message,Object result){
        return this.code(SUCCESS_CODE).message(message).result(result);
    }

    public ApiResult newApiResult(){
        ApiResult apiResult = new ApiResult();
        apiResult.setCode(this.code);
        apiResult.setMessage(this.message);
        apiResult.setResult(this.result);
        apiResult.setMessageId(this.messageId);
        return apiResult;
    }

}
