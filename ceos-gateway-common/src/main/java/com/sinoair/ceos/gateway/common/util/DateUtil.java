package com.sinoair.ceos.gateway.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: daxiong
 */
public class DateUtil {

    public static final String DATE_yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_yyyy_MM_dd_HH_mm = "yyyy-MM-dd HH:mm";
    public static final String DATE_yyyyMMddHHmmss = "yyyyMMddHHmmss";
    public static final String DATE_yyyyMMdd = "yyyyMMdd";
    public static final String DATE_yyyy_MM_dd = "yyyy-MM-dd";
    public static final String FIRST_DAY_OF_MONTH = "yyyy-MM-01";
    public static final String DATE_yyyy = "yyyy";
    public static final String DATE_MM = "MM";
    public static final String DATE_yyyy_MM = "yyyy-MM";
    public static final String DATE_dd = "dd";

    /**
     * 时区偏移计算
     */
    private static final Integer SYS_TIME_ZONE = TimeZone.getDefault().getRawOffset()/60/1000/60;

    /**
     * 转换时间
     *
     * @param pattern
     * @param d
     * @return
     * @throws ParseException
     */
    public static Date parseDate(String pattern, String d) throws ParseException {
        return new SimpleDateFormat(pattern).parse(d);
    }

    /**
     * 转换时间
     *
     * @param pattern
     * @param d
     * @return
     * @throws ParseException
     */
    public static String parseDateStr(String pattern, Date d) {
        return new SimpleDateFormat(pattern).format(d);
    }


    //两位年+两位月+两位日期+两位小时+两位分钟+两位秒
    public static String getYearMonthDayTimePoints() {
        Calendar cal = Calendar.getInstance();
        //当前年
        String year = String.valueOf(cal.get(Calendar.YEAR));
        year = year.substring(2, 4);
        //当前月
        String month = String.valueOf((cal.get(Calendar.MONTH)) + 1);
        if (month.length() == 1) {
            month = "0" + month;
        }
        //当前月的第几天：即当前日
        String day_of_month = String.valueOf(cal.get(Calendar.DAY_OF_MONTH));
        if (day_of_month.length() == 1) {
            day_of_month = "0" + day_of_month;
        }
        //当前时：HOUR_OF_DAY-24小时制；HOUR-12小时制
        String hour = String.valueOf(cal.get(Calendar.HOUR_OF_DAY));
        if (hour.length() == 1) {
            hour = "0" + hour;
        }
        //当前分
        String minute = String.valueOf(cal.get(Calendar.MINUTE));
        if (minute.length() == 1) {
            minute = "0" + minute;
        }
        String second = String.valueOf(cal.get(Calendar.SECOND));
        if (second.length() == 1){
            second = "0" + second;
        }
        String date = year + month + day_of_month + hour + minute+second;
        return date;
    }

    /**
     * 获取系统时区偏移值
     * @return
     */
    public static Integer getSysTimezone(){
        return SYS_TIME_ZONE;
    }

    /**
     * 时间转换为本地时间
     * @param localTimeZone 时区表示
     *                      0 时区
     *                      -1 西一区
     *                      8 东八区
     *                      12 东十二区
     * @return
     */
    public static Date getLocalTime(Date sourceDate, Integer localTimeZone){
        Calendar c1 = Calendar.getInstance();
        c1.setTime(sourceDate);
        Calendar c2 = ToolForDateTime.getCalendarByConvertTimeZone(c1, getSysTimezone(), localTimeZone);
        return c2.getTime();
    }

    /**
     * 获取年
     * @param date
     * @return
     */
    public static String getYear(Date date){
        return new SimpleDateFormat(DATE_yyyy).format(date);
    }

    /**
     * 获取月
     * @param date
     * @return
     */
    public static String getMonth(Date date){
        return new SimpleDateFormat(DATE_MM).format(date);
    }

    /**
     * 获取月份时间
     * @param date
     * @param offsetMonth
     * @return
     */
    public static List<String> getMonthList(Date date, Integer offsetMonth){
        List<String> list = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        if (offsetMonth < 0){
            for (int i = offsetMonth +1; i <= 0; i++) {
                calendar.setTime(date);
                calendar.add(Calendar.MONTH, i);
                String s = DateUtil.parseDateStr(DateUtil.DATE_yyyy_MM,calendar.getTime());
                list.add(s);
            }
        }else if (offsetMonth == 0){
            list.add(DateUtil.parseDateStr(DateUtil.DATE_yyyy_MM,date));
        }else{
            for (int i = 0; i < offsetMonth; i++) {
                calendar.setTime(date);
                calendar.add(Calendar.MONTH, i);
                String s = DateUtil.parseDateStr(DateUtil.DATE_yyyy_MM,calendar.getTime());
                list.add(s);
            }
        }
        return list;
    }

    /**
     * 获取本地时区的月份列表
     * @param date
     * @param offsetMonth
     * @param localTimezone
     * @return
     */
    public static List<String> getLocalMonthList(Date date, Integer offsetMonth, Integer localTimezone){
        Date d = getLocalTime(date,localTimezone);
        return getMonthList(d,offsetMonth);
    }


    /**
     * 获取TimeXone
     * @param offsetHour
     * @return
     */
    public static TimeZone getTimeZoneByOffsetHour(int offsetHour){
        return TimeZone.getTimeZone(getTimeZoneId(offsetHour));
    }

    /**
     * 获取timezoneId
     * @param offsetHour
     * @return
     */
    public static String getTimeZoneId(int offsetHour){
        String offset;
        if (offsetHour > 0){
            offset = "+"+offsetHour;
        }else{
            offset = String.valueOf(offsetHour);
        }
        String id = "GMT"+offset+":00";
        return id;
    }

    /**
     * 获取mysql时区偏移的字段
     * @param offsetHour
     * @return
     */
    public static String getTimezoneNoGMT(int offsetHour){
        return getTimeZoneId(offsetHour).replace("GMT","");
    }

    /**
     * 按照年偏移计算
     * @param date
     * @param offsetYear
     * @return
     */
    public static Date offsetYear(Date date, Integer offsetYear){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, offsetYear);
        return calendar.getTime();
    }

    /**
     * 按照月偏移计算
     * @param date
     * @param offsetMonth
     * @return
     */
    public static Date offsetMonth(Date date, Integer offsetMonth){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, offsetMonth);
        return calendar.getTime();
    }

}
