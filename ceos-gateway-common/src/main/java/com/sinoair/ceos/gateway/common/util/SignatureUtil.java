package com.sinoair.ceos.gateway.common.util;


import org.apache.commons.codec.digest.DigestUtils;

/**
 * @Author: daxiong
 */
public class SignatureUtil {

    /**
     * 校验签名
     * @param appKey
     * @param appSecret
     * @param resourceCode
     * @param requestBody
     * @param timestamp
     * @return
     */
    public static String createSign(String appKey,String appSecret,String resourceCode,String requestBody,Long timestamp){
        //return DigestUtils.md5Hex(timestamp+"|"+resourceCode + "|" + requestBody + "|" + appKey + "|" + appSecret);
        String stringBuilder = timestamp +
                "|" +
                resourceCode +
                "|" +
                requestBody +
                "|" +
                appKey +
                "|" +
                appSecret;
        return DigestUtils.md5Hex(stringBuilder);
    }

    /**
     * 校验签名
     * @param signature
     * @param appKey
     * @param appSecret
     * @param resourceCode
     * @param requestBody
     * @param timestamp
     * @return
     */
    public static boolean checkSign(String signature,String appKey,String appSecret,String resourceCode,String requestBody,Long timestamp){
        if (signature == null){
            return false;
        }
        return createSign(appKey,appSecret,resourceCode,requestBody,timestamp).equals(signature);
    }

}
