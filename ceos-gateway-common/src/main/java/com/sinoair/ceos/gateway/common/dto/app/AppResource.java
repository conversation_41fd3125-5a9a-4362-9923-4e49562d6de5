package com.sinoair.ceos.gateway.common.dto.app;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author: daxiong
 */
public class AppResource implements Serializable {

    private static final long serialVersionUID = -3139186857620085619L;

    /**
     * 资源code
     */
    private String resCode;

    /**
     * 资源名称
     */
    private String resName;

    /**
     * 对接应用：ceos、gtms、billing
     */
    private String resApp;

    /**
     * 资源接口地址
     */
    private String resUrl;

    /**
     * 资源地址请求方式：POST
     */
    private String resMethod;

    /**
     * 状态： 0 禁用、1 正常、2 升级中、3 审核中
     */
    private Integer resStatus;


    /**
     * 超时时间，毫秒，默认3000毫秒
     */
    private Integer timeout;

    /**
     * 默认header {}
     */
    private Map<String,String> resHeader;

    /**
     * 默认cookie {}
     */
    private Map<String,String> resCookie;

    /**
     * 默认form内容 {}
     */
    private Map<String,String> resFormData;


    /**
     * 是否有效 1 0
     */
    private Integer switchStatus;

    /**
     * 是否开启签名校验 1
     */
    private Integer switchSignature;

    /**
     * 是否开启时间戳校验 1
     */
    private Integer switchTimestamp;

    /**
     * 是否开启请求体校验 1 （json）
     */
    private Integer switchRequestBody;

    /**
     * 是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     */
    private Integer switchRateLimit;

    /**
     * 限流速度 speed/s
     */
    private Integer rateLimit;

    /**
     * 是否开启log存储
     */
    private Integer switchLog;

    /**
     * 日志存储天数
     */
    private Integer logDays;

    /**
     * 是否开启幂等
     */
    private Integer switchIdempotent;

    /**
     * 幂等超时时间，单位秒
     */
    private Integer idempotentTimeOut;

    public String getResCode() {
        return resCode;
    }

    public void setResCode(String resCode) {
        this.resCode = resCode;
    }

    public String getResName() {
        return resName;
    }

    public void setResName(String resName) {
        this.resName = resName;
    }

    public String getResApp() {
        return resApp;
    }

    public void setResApp(String resApp) {
        this.resApp = resApp;
    }

    public String getResUrl() {
        return resUrl;
    }

    public void setResUrl(String resUrl) {
        this.resUrl = resUrl;
    }

    public String getResMethod() {
        return resMethod;
    }

    public void setResMethod(String resMethod) {
        this.resMethod = resMethod;
    }

    public Integer getResStatus() {
        return resStatus;
    }

    public void setResStatus(Integer resStatus) {
        this.resStatus = resStatus;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Map<String, String> getResHeader() {
        return resHeader;
    }

    public void setResHeader(Map<String, String> resHeader) {
        this.resHeader = resHeader;
    }

    public Map<String, String> getResCookie() {
        return resCookie;
    }

    public void setResCookie(Map<String, String> resCookie) {
        this.resCookie = resCookie;
    }

    public Map<String, String> getResFormData() {
        return resFormData;
    }

    public void setResFormData(Map<String, String> resFormData) {
        this.resFormData = resFormData;
    }

    public Integer getSwitchStatus() {
        return switchStatus;
    }

    public void setSwitchStatus(Integer switchStatus) {
        this.switchStatus = switchStatus;
    }

    public Integer getSwitchSignature() {
        return switchSignature;
    }

    public void setSwitchSignature(Integer switchSignature) {
        this.switchSignature = switchSignature;
    }

    public Integer getSwitchTimestamp() {
        return switchTimestamp;
    }

    public void setSwitchTimestamp(Integer switchTimestamp) {
        this.switchTimestamp = switchTimestamp;
    }

    public Integer getSwitchRequestBody() {
        return switchRequestBody;
    }

    public void setSwitchRequestBody(Integer switchRequestBody) {
        this.switchRequestBody = switchRequestBody;
    }

    public Integer getSwitchRateLimit() {
        return switchRateLimit;
    }

    public void setSwitchRateLimit(Integer switchRateLimit) {
        this.switchRateLimit = switchRateLimit;
    }

    public Integer getRateLimit() {
        return rateLimit;
    }

    public void setRateLimit(Integer rateLimit) {
        this.rateLimit = rateLimit;
    }

    public Integer getSwitchLog() {
        return switchLog;
    }

    public void setSwitchLog(Integer switchLog) {
        this.switchLog = switchLog;
    }

    public Integer getLogDays() {
        return logDays;
    }

    public void setLogDays(Integer logDays) {
        this.logDays = logDays;
    }

    public Integer getSwitchIdempotent() {
        return switchIdempotent;
    }

    public void setSwitchIdempotent(Integer switchIdempotent) {
        this.switchIdempotent = switchIdempotent;
    }

    public Integer getIdempotentTimeOut() {
        return idempotentTimeOut;
    }

    public void setIdempotentTimeOut(Integer idempotentTimeOut) {
        this.idempotentTimeOut = idempotentTimeOut;
    }
}
