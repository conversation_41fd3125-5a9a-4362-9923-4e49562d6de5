package com.sinoair.ceos.gateway.common.dto.antd.table;

/**
 * @author: 大雄
 */
public class AntTableParam {

    /**
     * 通用查询字段
     */
    private String search;

    /**
     * 第几页
     */
    private Integer current = 1;

    /**
     * 每页多少条
     */
    private Integer pageSize = 10;

    /**
     * 排序的字段
     */
    private String sortField;

    /**
     * 排序方式
     */
    private String sortOrder;


    public Integer getCurrent() {
        return current;
    }

    public void setCurrent(Integer current) {
        this.current = current;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }
}
