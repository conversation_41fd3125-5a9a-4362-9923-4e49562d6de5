package com.sinoair.ceos.gateway.common.dto.app;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: daxiong
 */
public class AppInfo implements Serializable {


    private static final long serialVersionUID = 4269755936895742229L;
    /**
     * 应用ID
     */
    private String appKey;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用描述
     */
    private String appDescription;

    /**
     * 状态
     */
    private Integer appStatus;


    /**
     * 备注
     */
    private String remark;

    /**
     * 预留元数据
     */
    private String metaData;

    private List<AppResource> appResources;

    public List<AppResource> getAppResources() {
        return appResources;
    }

    public void setAppResources(List<AppResource> appResources) {
        this.appResources = appResources;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppDescription() {
        return appDescription;
    }

    public void setAppDescription(String appDescription) {
        this.appDescription = appDescription;
    }

    public Integer getAppStatus() {
        return appStatus;
    }

    public void setAppStatus(Integer appStatus) {
        this.appStatus = appStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMetaData() {
        return metaData;
    }

    public void setMetaData(String metaData) {
        this.metaData = metaData;
    }
}
