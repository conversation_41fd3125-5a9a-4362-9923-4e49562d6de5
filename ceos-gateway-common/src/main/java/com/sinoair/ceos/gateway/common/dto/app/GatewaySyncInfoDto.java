package com.sinoair.ceos.gateway.common.dto.app;

import com.sinoair.ceos.gateway.common.dto.proxy.GatewayProxySyncInfo;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: daxiong
 */
public class GatewaySyncInfoDto implements Serializable {

    private static final long serialVersionUID = 5049535170745821316L;
    private List<AppInfo> appInfos;

    private String gatewayVersion;

    /**
     * 扩展代理信息
     */
    private GatewayProxySyncInfo gatewayProxySyncInfo;

    public List<AppInfo> getAppInfos() {
        return appInfos;
    }

    public void setAppInfos(List<AppInfo> appInfos) {
        this.appInfos = appInfos;
    }

    public String getGatewayVersion() {
        return gatewayVersion;
    }

    public void setGatewayVersion(String gatewayVersion) {
        this.gatewayVersion = gatewayVersion;
    }

    public GatewayProxySyncInfo getGatewayProxySyncInfo() {
        return gatewayProxySyncInfo;
    }

    public void setGatewayProxySyncInfo(GatewayProxySyncInfo gatewayProxySyncInfo) {
        this.gatewayProxySyncInfo = gatewayProxySyncInfo;
    }
}
