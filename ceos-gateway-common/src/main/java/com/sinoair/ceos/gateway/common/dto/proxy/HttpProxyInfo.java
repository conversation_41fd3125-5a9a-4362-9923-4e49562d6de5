package com.sinoair.ceos.gateway.common.dto.proxy;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @author: 大雄
 */
public class HttpProxyInfo implements Serializable {

    private static final long serialVersionUID = 6228361730886709739L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 唯一键
     */
    private String proxyUid;

    /**
     * 代理编码
     */
    private String proxyCode;

    /**
     * 代理名称
     */
    private String proxyName;

    /**
     * 反向代理：reverse 正向代理：origin
     */
    private String proxyType;

    /**
     * 状态
     */
    private Integer proxyStatus;

    /**
     * 目标url
     */
    private String targetUrl;

    /**
     * 目标类型text, redirect
     */
    private String targetType;

    /**
     * 请求方式: GET POST DELETE...
     */
    private String targetMethod;

    /**
     * 超时时间
     */
    private Integer targetTimeout;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否开启限流保护 0 关闭、1 本地限流器、2 集群限流器
     */
    private Integer switchRateLimit;

    /**
     * 限流速度 speed/s
     */
    private Integer rateLimit;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date handleTime;

    /**
     * 更新人
     */
    private String handler;

    /**
     * 负责人
     */
    private String coder;

    /**
     * 请求头列表
     */
    private String targetHeaders;

    /**
     * 请求参数列表
     */
    private String targetForms;

    /**
     * 预留元数据
     */
    private String metaData;

    /**
     * 是否开启log存储
     */
    private Integer switchLog;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProxyUid() {
        return proxyUid;
    }

    public void setProxyUid(String proxyUid) {
        this.proxyUid = proxyUid;
    }

    public String getProxyCode() {
        return proxyCode;
    }

    public void setProxyCode(String proxyCode) {
        this.proxyCode = proxyCode;
    }

    public String getProxyName() {
        return proxyName;
    }

    public void setProxyName(String proxyName) {
        this.proxyName = proxyName;
    }

    public String getProxyType() {
        return proxyType;
    }

    public void setProxyType(String proxyType) {
        this.proxyType = proxyType;
    }

    public Integer getProxyStatus() {
        return proxyStatus;
    }

    public void setProxyStatus(Integer proxyStatus) {
        this.proxyStatus = proxyStatus;
    }

    public String getTargetUrl() {
        return targetUrl;
    }

    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    public String getTargetType() {
        return targetType;
    }

    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public String getTargetMethod() {
        return targetMethod;
    }

    public void setTargetMethod(String targetMethod) {
        this.targetMethod = targetMethod;
    }

    public Integer getTargetTimeout() {
        return targetTimeout;
    }

    public void setTargetTimeout(Integer targetTimeout) {
        this.targetTimeout = targetTimeout;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSwitchRateLimit() {
        return switchRateLimit;
    }

    public void setSwitchRateLimit(Integer switchRateLimit) {
        this.switchRateLimit = switchRateLimit;
    }

    public Integer getRateLimit() {
        return rateLimit;
    }

    public void setRateLimit(Integer rateLimit) {
        this.rateLimit = rateLimit;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getHandler() {
        return handler;
    }

    public void setHandler(String handler) {
        this.handler = handler;
    }

    public String getCoder() {
        return coder;
    }

    public void setCoder(String coder) {
        this.coder = coder;
    }

    public String getTargetHeaders() {
        return targetHeaders;
    }

    public void setTargetHeaders(String targetHeaders) {
        this.targetHeaders = targetHeaders;
    }

    public String getTargetForms() {
        return targetForms;
    }

    public void setTargetForms(String targetForms) {
        this.targetForms = targetForms;
    }

    public String getMetaData() {
        return metaData;
    }

    public void setMetaData(String metaData) {
        this.metaData = metaData;
    }

    public Integer getSwitchLog() {
        return switchLog;
    }

    public void setSwitchLog(Integer switchLog) {
        this.switchLog = switchLog;
    }
}
